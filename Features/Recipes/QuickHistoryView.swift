import SwiftUI

struct QuickHistoryView: View {
    @State private var quickItems: [QuickResultHistory] = []
    @State private var showManageInfo: Bool = false
    @State private var currentIndex: Int = 0

    var body: some View {
        VStack(spacing: 8) {
            // Capacity header always visible
            CapacityIndicatorView(
                count: quickItems.count,
                capacity: QuickHistoryManager.shared.capacity,
                onManage: { showManageInfo = true }
            )
            .padding(.horizontal)
            .padding(.top, 12)

            Group {
                if quickItems.isEmpty {
                    QuickEmptyStateView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.vertical, 24)
                } else {
                    // Horizontal paging cards with vertical scrolling inside pages
                    HorizontalQuickResultsView(items: quickItems, currentIndex: $currentIndex)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .accessibilityIdentifier("quick_horizontal_pager")

                    // Page indicator (dots by default)
                    PageIndicatorView(count: quickItems.count, currentIndex: currentIndex, mode: .dots)
                        .padding(.bottom, 8)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .onAppear { reload() }
        .onChange(of: quickItems.count) { _, _ in
            // Ensure index stays in range when data changes
            currentIndex = min(max(currentIndex, 0), max(quickItems.count - 1, 0))
        }
        .animation(.default, value: quickItems.count)
        .alert("Manage Quick History", isPresented: $showManageInfo) {
            Button("OK", role: .cancel) {}
        } message: {
            Text("Manage selection and batch actions will be added in a later task. For now, use future Manage mode to delete items.")
        }
        .accessibilityLabel("Quick history horizontal view")
    }

    private func reload() {
        quickItems = QuickHistoryManager.shared.all()
        currentIndex = 0
    }
}

#Preview("Quick History") {
    NavigationStack { QuickHistoryView() }
}
