import SwiftUI

/// Container view for the static Plans calendar matrix.
/// Renders week indicator and 7×3 grid, and handles navigation to slot recipes.
struct MealPlanCalendarView: View {
    let plan: MealPlan
    @State private var selected: SelectedSlot?

    private let calendar = Calendar.current

    var body: some View {
        let week = WeeklyMealPlan(referenceDate: Date(), calendar: calendar)
        let index = indexPlanByDateAndMeal(plan: plan, calendar: calendar)
        let weekTitle = WeeklyMealPlan.formatRange(week.weekStart, week.weekEnd, calendar: calendar)

        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text(weekTitle)
                    .font(.headline)
                Spacer()
            }
            .padding(.horizontal)
            .padding(.top, 8)

            CalendarMatrixView(
                weekDays: week.days,
                slotsProvider: { date, meal in
                    let key = dayKey(for: date, calendar: calendar)
                    let byMeal = index[key] ?? [:]
                    return byMeal[meal] ?? []
                },
                favoritesProvider: { id in FavoritesStore.shared.isFavorite(id: id) },
                onSelectSlot: { date, meal, slots in
                    selected = SelectedSlot(date: date, mealType: meal, slots: slots)
                }
            )
            .padding(.horizontal)
        }
        .navigationDestination(item: $selected) { selection in
            SlotRecipesListView(date: selection.date, mealType: selection.mealType, slots: selection.slots)
        }
    }

    // MARK: - Indexing helpers

    private func dayKey(for date: Date, calendar: Calendar) -> String {
        let d = calendar.startOfDay(for: date)
        let f = Self.yyyyMMdd
        return f.string(from: d)
    }

    private func indexPlanByDateAndMeal(plan: MealPlan, calendar: Calendar) -> [String: [MealType: [MealSlot]]] {
        var result: [String: [MealType: [MealSlot]]] = [:]
        for day in plan.days {
            let key = dayKey(for: day.date, calendar: calendar)
            for slot in day.meals {
                result[key, default: [:]][slot.mealType, default: []].append(slot)
            }
        }
        return result
    }

    private static let yyyyMMdd: DateFormatter = {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        f.locale = Locale(identifier: "en_US_POSIX")
        f.timeZone = TimeZone(secondsFromGMT: 0)
        return f
    }()
}

// MARK: - Selection & List

private struct SelectedSlot: Identifiable, Hashable {
    let id = UUID()
    let date: Date
    let mealType: MealType
    let slots: [MealSlot]

    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(date)
        hasher.combine(mealType)
        hasher.combine(slots.map { $0.slotId })
    }

    // Equatable conformance
    static func == (lhs: SelectedSlot, rhs: SelectedSlot) -> Bool {
        return lhs.id == rhs.id &&
               lhs.date == rhs.date &&
               lhs.mealType == rhs.mealType &&
               lhs.slots.map { $0.slotId } == rhs.slots.map { $0.slotId }
    }
}

/// Displays all recipes in a selected (date × meal) slot as a list with navigation to detail.
private struct SlotRecipesListView: View {
    let date: Date
    let mealType: MealType
    let slots: [MealSlot]

    var body: some View {
        List {
            Section(header: Text(headerTitle)) {
                ForEach(slots) { slot in
                    NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: slot.recipe)) {
                        HStack(spacing: 12) {
                            Image(systemName: iconName(for: slot.mealType))
                                .foregroundStyle(.orange)
                            VStack(alignment: .leading, spacing: 4) {
                                Text(slot.recipe.title).font(.headline)
                                if let subtitle = slot.recipe.subtitle, !subtitle.isEmpty {
                                    Text(subtitle).font(.subheadline).foregroundStyle(.secondary)
                                }
                            }
                            Spacer()
                            Image(systemName: "chevron.right").foregroundStyle(.secondary)
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
        }
        .navigationTitle(mealType.displayName)
    }

    private var headerTitle: String {
        let df = DateFormatter()
        df.dateStyle = .medium
        return df.string(from: date)
    }

    private func iconName(for meal: MealType) -> String {
        switch meal {
        case .breakfast: return "sunrise.fill"
        case .lunch: return "sun.max.fill"
        case .dinner: return "moon.fill"
        }
    }
}

#Preview("MealPlanCalendarView (Empty)") {
    NavigationStack { MealPlanCalendarView(plan: MealPlan(days: [])) }
}
