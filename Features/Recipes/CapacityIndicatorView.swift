import SwiftUI

struct CapacityIndicatorView: View {
    let count: Int
    let capacity: Int
    var onManage: (() -> Void)? = nil

    private var color: Color {
        if count >= capacity { return .red }
        if count >= 8 { return .yellow }
        return .primary
    }

    var body: some View {
        HStack(alignment: .center, spacing: 12) {
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text("Quick History")
                        .font(.title3.weight(.semibold))
                    Spacer()
                    Text("[\(count)/\(capacity)]")
                        .font(.subheadline.monospacedDigit())
                        .foregroundStyle(color)
                        .accessibilityLabel("Capacity \(count) of \(capacity)")
                }
                HStack(spacing: 4) {
                    ForEach(0..<capacity, id: \.self) { idx in
                        Circle()
                            .fill(idx < count ? color : Color.secondary.opacity(0.25))
                            .frame(width: 6, height: 6)
                    }
                }
                .accessibilityHidden(true)
            }
            if let onManage {
                Spacer(minLength: 12)
                <PERSON><PERSON>("Manage", action: onManage)
                    .buttonStyle(.bordered)
                    .tint(color == .primary ? .accentColor : color)
                    .accessibilityIdentifier("quick_capacity_manage")
            }
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        CapacityIndicatorView(count: 3, capacity: 10)
        CapacityIndicatorView(count: 8, capacity: 10)
        CapacityIndicatorView(count: 10, capacity: 10)
    }
    .padding()
}

