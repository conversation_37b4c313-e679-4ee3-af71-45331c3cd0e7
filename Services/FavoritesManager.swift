import Foundation

@MainActor
final class FavoritesManager {
    static let shared = FavoritesManager()

    private let store: FavoritesStore
    private let quickHistory: QuickHistoryManager
    private let planStore: PlanStore

    private init(
        store: FavoritesStore = .shared,
        quickHistory: QuickHistoryManager = .shared,
        planStore: PlanStore = .shared
    ) {
        self.store = store
        self.quickHistory = quickHistory
        self.planStore = planStore
    }

    // MARK: - Mutations

    func toggleFavorite(id: String) {
        store.toggleFavorite(id: id)
    }

    func isFavorite(id: String) -> Bool {
        store.isFavorite(id: id)
    }

    // MARK: - Queries

    /// Unified list of favorites built from Quick history and last saved Meal Plan.
    /// Deduplicated by recipe ID using FavoritesStore order (newest-first).
    func allItems() -> [FavoriteItem] {
        let ids = store.all() // newest-first IDs
        let quick = quickHistory.all()
        let plan = planStore.loadLastMealPrep()?.plan

        // Pre-index for faster lookup
        var quickIndex: [String: [(id: UUID, at: Date, model: RecipeUIModel)]] = [:]
        for entry in quick {
            for recipe in entry.recipes {
                quickIndex[recipe.id, default: []].append((entry.id, entry.generatedAt, recipe))
            }
        }

        var planIndex: [String: [(slotId: UUID, date: Date, meal: MealType, model: RecipeUIModel)]] = [:]
        if let plan = plan {
            for day in plan.days {
                for slot in day.meals {
                    let r = slot.recipe
                    planIndex[r.id, default: []].append((slot.slotId, day.date, slot.mealType, r))
                }
            }
        }

        var results: [FavoriteItem] = []
        for id in ids {
            var contexts: [FavoriteOrigin] = []
            var title: String = id

            if let quickHits = quickIndex[id] {
                contexts.append(contentsOf: quickHits.map { .quick(quickId: $0.id, generatedAt: $0.at) })
                // Prefer title from a UI model
                if let first = quickHits.first { title = first.model.title }
            }

            if let planHits = planIndex[id] {
                contexts.append(contentsOf: planHits.map { .plan(slotId: $0.slotId, date: $0.date, meal: $0.meal) })
                if title == id, let first = planHits.first { title = first.model.title }
            }

            results.append(FavoriteItem(id: id, title: title, contexts: contexts))
        }
        return results
    }

    /// Resolve a UI model for a favorite recipe ID by searching Plans first, then Quick.
    func resolveUIModel(for id: String) -> RecipeUIModel? {
        if let plan = planStore.loadLastMealPrep()?.plan {
            for day in plan.days {
                if let hit = day.meals.first(where: { $0.recipe.id == id }) {
                    return hit.recipe
                }
            }
        }
        let quick = quickHistory.all()
        for entry in quick {
            if let hit = entry.recipes.first(where: { $0.id == id }) {
                return hit
            }
        }
        return nil
    }
}

