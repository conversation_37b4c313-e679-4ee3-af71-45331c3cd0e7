import Foundation

@MainActor
final class QuickHistoryManager {
    static let shared = QuickHistoryManager()

    private let defaults = UserDefaults.standard
    private let historyKey = "quick.history.v1"

    private let encoder: JSONEncoder = {
        let e = JSONEncoder()
        e.dateEncodingStrategy = .iso8601
        return e
    }()

    private let decoder: J<PERSON>NDecoder = {
        let d = JSONDecoder()
        d.dateDecodingStrategy = .iso8601
        return d
    }()

    // MARK: - Public API

    /// Returns all saved quick results, newest first.
    func all() -> [QuickResultHistory] {
        guard let data = defaults.data(forKey: historyKey) else { return [] }
        if let items = try? decoder.decode([QuickResultHistory].self, from: data) {
            return items
        }
        return []
    }

    /// Returns latest quick result if any
    func latest() -> QuickResultHistory? { all().first }

    /// Current count of saved quick results
    var count: Int { all().count }

    /// Maximum allowed quick history items
    var capacity: Int { 10 }

    /// Whether a new item can be saved (capacity not full)
    var canAcceptNew: Bool { count < capacity }

    enum SaveOutcome { case saved, full }

    /// Save a new quick history item at position 0 (newest first).
    /// Does not auto-evict when at capacity; instead returns `.full` and performs no save.
    @discardableResult
    func save(_ item: QuickResultHistory) -> SaveOutcome {
        var items = all()
        guard items.count < capacity else { return .full }
        items.insert(item, at: 0)
        persist(items)
        return .saved
    }

    /// Delete an item by ID
    func delete(id: UUID) {
        var items = all()
        items.removeAll { $0.id == id }
        persist(items)
    }

    /// Replace all items (atomic)
    func replaceAll(_ items: [QuickResultHistory]) {
        let trimmed = Array(items.prefix(capacity))
        persist(trimmed)
    }

    // MARK: - Persistence
    private func persist(_ items: [QuickResultHistory]) {
        if let data = try? encoder.encode(items) {
            defaults.set(data, forKey: historyKey)
        }
    }
}

