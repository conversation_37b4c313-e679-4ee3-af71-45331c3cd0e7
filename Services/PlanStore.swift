import Foundation

// MARK: - PlanStore v1 (UserDefaults JSON)
// Stores last Quick generation and last Meal Prep plan per PRD v3 Phase 2

@MainActor
final class PlanStore {
    static let shared = PlanStore()
    private let defaults = UserDefaults.standard

    private let quickKey = "planstore.lastQuick.v1"
    private let mealPrepKey = "planstore.lastMealPrep.v1"

    private let encoder: JSONEncoder = {
        let e = JSONEncoder()
        e.dateEncodingStrategy = .iso8601
        return e
    }()
    private let decoder: JSONDecoder = {
        let d = JSONDecoder()
        d.dateDecodingStrategy = .iso8601
        return d
    }()

    // MARK: - Public API

    func saveLastQuick(config: QuickConfiguration, recipes: [RecipeUIModel]) {
        let model = LastQuick(
            mealType: config.mealType,
            numberOfDishes: config.numberOfDishes,
            totalCookTime: config.totalTimeMinutes,
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest,
            generatedAt: Date(),
            recipes: recipes
        )
        if let data = try? encoder.encode(model) {
            defaults.set(data, forKey: quickKey)
        }
    }

    func loadLastQuick() -> LastQuick? {
        guard let data = defaults.data(forKey: quickKey) else { return nil }
        return try? decoder.decode(LastQuick.self, from: data)
    }

    func saveLastMealPrep(config: CustomConfiguration, recipes: [RecipeUIModel]) {
        let plan = MealPlanBuilder.buildPlan(from: config, recipes: recipes)
        let model = LastMealPrep(
            days: config.days,
            selectedMeals: Array(config.selectedMeals),
            perMealConfigs: Dictionary(uniqueKeysWithValues: config.mealConfigurations.map { ($0.key.rawValue, $0.value) }),
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest,
            generatedAt: Date(),
            plan: plan
        )
        if let data = try? encoder.encode(model) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

    func loadLastMealPrep() -> LastMealPrep? {
        guard let data = defaults.data(forKey: mealPrepKey) else { return nil }
        return try? decoder.decode(LastMealPrep.self, from: data)
    }

    // MARK: - V6 Structured Save with Overlap Policy
    /// Merge a new structured plan into the last saved plan using overlap policy:
    /// - Replace non-favorited overlapping slots
    /// - Skip favorited overlapping slots
    /// - Append new slots where there is no overlap
    /// Operation is atomic (single write).
    /// Returns a summary for UI banner.
    func mergeAndSave(newPlan: MealPlan) -> OverlapSaveSummary {
        guard !newPlan.days.isEmpty else { return OverlapSaveSummary(replaced: 0, skippedFavorites: 0, added: 0) }

        // Load existing plan if any
        let old = loadLastMealPrep()
        let oldPlan = old?.plan

        // Build merged plan by date and meal type
        let mergedDays = mergePlans(oldPlan: oldPlan, newPlan: newPlan)

        // Reindex dayIndex and normalize scheduledDate
        let normalizedDays = normalizeDayIndicesAndDates(mergedDays)

        let summary = overlapSummary // captured during merge

        // Persist atomically
        let updated = LastMealPrep(
            days: normalizedDays.count,
            selectedMeals: deduceSelectedMeals(from: normalizedDays),
            perMealConfigs: old?.perMealConfigs ?? [:],
            cuisines: old?.cuisines ?? [],
            additionalRequest: old?.additionalRequest,
            generatedAt: Date(),
            plan: MealPlan(days: normalizedDays)
        )
        if let data = try? encoder.encode(updated) {
            defaults.set(data, forKey: mealPrepKey)
        }
        return summary
    }

    // MARK: - Internal merge state
    private var overlapSummary = OverlapSaveSummary(replaced: 0, skippedFavorites: 0, added: 0)

    private func mergePlans(oldPlan: MealPlan?, newPlan: MealPlan) -> [DayPlan] {
        overlapSummary = OverlapSaveSummary(replaced: 0, skippedFavorites: 0, added: 0)
        let fmt = Self.yyyyMMdd

        // Index by date string
        var oldByDate: [String: DayPlan] = [:]
        if let old = oldPlan {
            for d in old.days { oldByDate[fmt.string(from: d.date)] = d }
        }
        var newByDate: [String: DayPlan] = [:]
        for d in newPlan.days { newByDate[fmt.string(from: d.date)] = d }

        let allDateKeys = Set(oldByDate.keys).union(newByDate.keys).sorted()
        var merged: [DayPlan] = []
        for key in allDateKeys {
            let date = fmt.date(from: key) ?? Date()
            let oldDay = oldByDate[key]
            let newDay = newByDate[key]
            let mergedMeals = mergeMeals(oldDay?.meals ?? [], newDay?.meals ?? [])
            merged.append(DayPlan(date: date, meals: mergedMeals))
        }
        return merged
    }

    private func mergeMeals(_ oldMeals: [MealSlot], _ newMeals: [MealSlot]) -> [MealSlot] {
        // Group by meal type
        let oldGroups = Dictionary(grouping: oldMeals, by: { $0.mealType })
        let newGroups = Dictionary(grouping: newMeals, by: { $0.mealType })
        let mealTypes = Set(oldGroups.keys).union(newGroups.keys)
        var result: [MealSlot] = []
        for meal in mealTypes {
            let oldArray = oldGroups[meal] ?? []
            let newArray = newGroups[meal] ?? []
            let merged = mergeSlotsForMeal(oldArray, newArray, meal: meal)
            result.append(contentsOf: merged)
        }
        return result
    }

    private func mergeSlotsForMeal(_ oldArray: [MealSlot], _ newArray: [MealSlot], meal: MealType) -> [MealSlot] {
        var result = oldArray
        var used = Array(repeating: false, count: oldArray.count)
        for newSlot in newArray {
            // Try replacing first non-favorite old slot of same meal not yet used
            var replaced = false
            for i in 0..<result.count where !used[i] {
                let slot = result[i]
                let isFav = FavoritesStore.shared.isFavorite(id: slot.recipe.id)
                if !isFav && slot.mealType == meal {
                    // Replace recipe, keep slotId
                    let updatedRecipe = newSlot.recipe
                    let newMealSlot = MealSlot(slotId: slot.slotId, dayIndex: slot.dayIndex, mealType: meal, recipe: updatedRecipe)
                    result[i] = newMealSlot
                    used[i] = true
                    replaced = true
                    overlapSummary.replaced += 1
                    break
                }
            }
            if !replaced {
                // If cannot replace (all favorited), append as new slot
                result.append(MealSlot(slotId: UUID(), dayIndex: newSlot.dayIndex, mealType: meal, recipe: newSlot.recipe))
                if oldArray.contains(where: { $0.mealType == meal }) {
                    // There was overlap but all were favorited
                    overlapSummary.skippedFavorites += 1
                } else {
                    // Pure addition (no overlap)
                    overlapSummary.added += 1
                }
            }
        }
        return result
    }

    private func normalizeDayIndicesAndDates(_ days: [DayPlan]) -> [DayPlan] {
        let sorted = days.sorted { $0.date < $1.date }
        var normalized: [DayPlan] = []
        for (idx, day) in sorted.enumerated() {
            let newMeals = day.meals.map { slot -> MealSlot in
                let updatedRecipe = RecipeUIModel(
                    id: slot.recipe.id,
                    title: slot.recipe.title,
                    subtitle: slot.recipe.subtitle,
                    estimatedTime: slot.recipe.estimatedTime,
                    imageURL: slot.recipe.imageURL,
                    ingredientsFromPantry: slot.recipe.ingredientsFromPantry,
                    additionalIngredients: slot.recipe.additionalIngredients,
                    difficulty: slot.recipe.difficulty,
                    mealType: slot.mealType,
                    dayIndex: idx,
                    servings: slot.recipe.servings,
                    cuisine: slot.recipe.cuisine,
                    scheduledDate: day.date
                )
                return MealSlot(slotId: slot.slotId, dayIndex: idx, mealType: slot.mealType, recipe: updatedRecipe)
            }
            normalized.append(DayPlan(date: day.date, meals: newMeals))
        }
        return normalized
    }

    private func deduceSelectedMeals(from days: [DayPlan]) -> [MealType] {
        var set = Set<MealType>()
        for d in days { for s in d.meals { set.insert(s.mealType) } }
        return Array(set).sorted { $0.rawValue < $1.rawValue }
    }

    private static let yyyyMMdd: DateFormatter = {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        f.locale = Locale(identifier: "en_US_POSIX")
        f.timeZone = TimeZone(secondsFromGMT: 0)
        return f
    }()

    /// Replace recipes for specific MealSlot IDs in the last saved Meal Prep plan
    func replaceRecipesInLastMealPrep(_ replacements: [UUID: RecipeUIModel]) {
        guard let old = loadLastMealPrep() else { return }
        let newDays: [DayPlan] = old.plan.days.map { day in
            let newMeals = day.meals.map { slot in
                if let rep = replacements[slot.slotId] {
                    return MealSlot(slotId: slot.slotId, dayIndex: slot.dayIndex, mealType: slot.mealType, recipe: rep)
                } else {
                    return slot
                }
            }
            return DayPlan(date: day.date, meals: newMeals)
        }
        let newPlan = MealPlan(days: newDays)
        let updated = LastMealPrep(
            days: old.days,
            selectedMeals: old.selectedMeals,
            perMealConfigs: old.perMealConfigs,
            cuisines: old.cuisines,
            additionalRequest: old.additionalRequest,
            generatedAt: Date(),
            plan: newPlan
        )
        if let data = try? encoder.encode(updated) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

    /// Delete specified meal slots (by slotId) from the last saved Meal Prep plan (atomic write).
    func deleteSlotsInLastMealPrep(_ slotIds: Set<UUID>) {
        guard let old = loadLastMealPrep(), !slotIds.isEmpty else { return }
        let newDays: [DayPlan] = old.plan.days.map { day in
            let newMeals = day.meals.filter { !slotIds.contains($0.slotId) }
            return DayPlan(date: day.date, meals: newMeals)
        }
        let newPlan = MealPlan(days: newDays)
        let updated = LastMealPrep(
            days: old.days,
            selectedMeals: old.selectedMeals,
            perMealConfigs: old.perMealConfigs,
            cuisines: old.cuisines,
            additionalRequest: old.additionalRequest,
            generatedAt: Date(),
            plan: newPlan
        )
        if let data = try? encoder.encode(updated) {
            defaults.set(data, forKey: mealPrepKey)
        }
    }

}

// MARK: - Models

struct LastQuick: Codable, Equatable {
    let mealType: MealType
    let numberOfDishes: Int
    let totalCookTime: Int
    let cuisines: [String]
    let additionalRequest: String?
    let generatedAt: Date
    let recipes: [RecipeUIModel]
}

struct LastMealPrep: Codable, Equatable {
    let days: Int
    let selectedMeals: [MealType]
    let perMealConfigs: [String: MealConfig]
    let cuisines: [String]
    let additionalRequest: String?
    let generatedAt: Date
    let plan: MealPlan
}


struct MealPlan: Codable, Equatable {
    let days: [DayPlan]
}

struct DayPlan: Codable, Equatable, Identifiable {
    var id: UUID { UUID() }
    let date: Date
    let meals: [MealSlot]
}

struct MealSlot: Codable, Equatable, Identifiable, Hashable {
    let slotId: UUID
    let dayIndex: Int
    let mealType: MealType
    let recipe: RecipeUIModel
    var id: UUID { slotId }

    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(slotId)
    }
}

// MARK: - Builder

enum MealPlanBuilder {
    static func buildPlan(from config: CustomConfiguration, recipes: [RecipeUIModel]) -> MealPlan {
        let days = max(1, config.days)
        let selectedMeals = config.selectedMeals.isEmpty ? Set(MealType.allCases) : config.selectedMeals
        let sortedMeals = selectedMeals.sorted { $0.rawValue < $1.rawValue }

        // Compute required slots per day based on per-meal configs
        var perDaySlots: [(MealType, Int)] = []
        for meal in sortedMeals {
            let cfg = config.mealConfigurations[meal] ?? MealConfig()
            perDaySlots.append((meal, max(1, min(6, cfg.numberOfDishes))))
        }

        var recipeIterator = recipes.makeIterator()
        var dayPlans: [DayPlan] = []
        let calendar = Calendar.current
        let today = Date()

        for dayIndex in 0..<days {
            let date = calendar.date(byAdding: .day, value: dayIndex, to: today) ?? today
            var slots: [MealSlot] = []
            for (meal, count) in perDaySlots {
                for _ in 0..<count {
                    guard let r = recipeIterator.next() else { break }
                    let slot = MealSlot(slotId: UUID(), dayIndex: dayIndex, mealType: meal, recipe: r)
                    slots.append(slot)
                }
            }
            dayPlans.append(DayPlan(date: date, meals: slots))
        }
        return MealPlan(days: dayPlans)
    }
}

// MARK: - Overlap Summary

struct OverlapSaveSummary: Codable, Equatable {
    var replaced: Int
    var skippedFavorites: Int
    var added: Int
}
