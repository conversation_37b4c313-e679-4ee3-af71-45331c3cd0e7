import Foundation

/// Represents a favorited recipe and its origin contexts across Quick and Plans.
struct FavoriteItem: Identifiable, Hashable {
    /// The underlying recipe identifier (stable across contexts)
    let id: String
    let title: String
    let contexts: [FavoriteOrigin]

    /// Prefer a more specific context for display if available
    var primaryContext: FavoriteOrigin? { contexts.first }
}

enum FavoriteOrigin: Hashable {
    case quick(quickId: UUID, generatedAt: Date)
    case plan(slotId: UUID, date: Date, meal: MealType)
}

