import Foundation

/// Lightweight model for a single Quick generation history entry
/// Ke<PERSON> intentionally similar to `LastQuick` for compatibility, but with a stable ID
struct QuickResultHistory: Codable, Equatable, Identifiable {
    let id: UUID
    let mealType: MealType
    let numberOfDishes: Int
    let totalCookTime: Int
    let cuisines: [String]
    let additionalRequest: String?
    let generatedAt: Date
    let recipes: [RecipeUIModel]

    init(
        id: UUID = UUID(),
        mealType: MealType,
        numberOfDishes: Int,
        totalCookTime: Int,
        cuisines: [String],
        additionalRequest: String?,
        generatedAt: Date = Date(),
        recipes: [RecipeUIModel]
    ) {
        self.id = id
        self.mealType = mealType
        self.numberOfDishes = numberOfDishes
        self.totalCookTime = totalCookTime
        self.cuisines = cuisines
        self.additionalRequest = additionalRequest
        self.generatedAt = generatedAt
        self.recipes = recipes
    }
}

