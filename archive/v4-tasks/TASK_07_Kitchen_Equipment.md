# Task 07: Implement Kitchen Equipment Preference

## 1. Scope

This task involves creating a user interface for managing kitchen equipment and integrating it into the user's profile. The goal is to allow users to specify which kitchen tools they own, so this data can be used to generate more relevant recipes.

- **UI Development:** Create a new SwiftUI view (`KitchenEquipmentView.swift`) that displays the comprehensive, categorized list of kitchen appliances and durable cookware.
- **State Management:** Create a corresponding ViewModel (`KitchenEquipmentViewModel.swift`) to manage the selection state and interaction logic.
- **Integration:** Add a navigation entry point from the existing `PreferencesEditView.swift` to the new `KitchenEquipmentView`.
- **Data Persistence:** Ensure that the user's selections are saved to the `equipmentOwned: [String]` array within the `UserPreferences` model. The `UserProfileService` should handle the persistence to Firestore.

## 2. Rationale

As per our AI-assisted analysis, knowing the user's available kitchen equipment is a critical parameter for personalizing recipe generation. By providing this information to the Gemini model, the application can avoid suggesting recipes that require tools the user does not have (e.g., a sous-vide recipe for a user without a sous-vide circulator). This directly enhances the core user value of receiving practical, cookable meal plans.

## 3. File Pointers

- **Model to Update:**
  - `Models/UserPreferences.swift` (The `equipmentOwned` property is already defined here).
- **Views to Create/Modify:**
  - `Features/Profile/KitchenEquipmentView.swift` (New file)
  - `Features/Profile/PreferencesEditView.swift` (Modify to add navigation link)
- **ViewModel to Create:**
  - `Features/Profile/KitchenEquipmentViewModel.swift` (New file)
- **Services to Use:**
  - `Services/UserProfileService.swift` (To save the updated `UserPreferences`).
  - `Services/RecipeGenerationService.swift` (To verify the data is eventually sent).

## 4. Acceptance Criteria

- [ ] A new row item, labeled "My Kitchen Equipment" or similar, appears in the `PreferencesEditView`.
- [ ] Tapping this row navigates the user to the new `KitchenEquipmentView`.
- [ ] The `KitchenEquipmentView` correctly displays the two categorized lists ("Appliances" and "Durable Cookware") as discussed.
- [ ] The user's currently saved equipment selections are correctly reflected with a checkmark or similar indicator when the view loads.
- [ ] The user can tap on any equipment item to select or deselect it.
- [ ] Changes are saved automatically or via a "Save" button, updating the `equipmentOwned` array in the `UserPreferences` object.
- [ ] The updated preferences are successfully persisted through the `UserProfileService`.

## 5. Validation

- **Manual Testing:**
  1. Navigate to the Profile tab -> Preferences -> My Kitchen Equipment.
  2. Select 5-10 items from both categories and go back.
  3. Re-enter the screen and verify that your selections have been saved correctly.
  4. Close and reopen the app to ensure persistence across sessions.
  5. (Debug) When generating a recipe, confirm via logs or a debug panel that the `equipmentOwned` array in the `RecipeGenerationRequest` payload matches your selections.

- **Unit/UI Testing (Recommended):**
  - Write a unit test for `KitchenEquipmentViewModel` to confirm that the `equipmentOwned` array is correctly modified upon selection.
  - Write a UI test to validate the navigation from `PreferencesEditView` to `KitchenEquipmentView` and to check that tapping an item toggles its selection state visually.
