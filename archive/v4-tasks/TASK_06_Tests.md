# TASK 06: Tests – Unit and UI

Objective
- Add/adjust tests to cover V4 behavior changes for Pantry highlights, Add sheet UX, duplicates policy, Recipe/Generator fixes, and prompt parsers.

Scope
- Tests/*

Steps
1) Unit tests
- PantryServiceHighlightsTests: markAsRecentlyAdded sets/clears flags after 5s; isRecentlyAdded used in UI.
- PantryDuplicatesTests: saving near-duplicates keeps both entries.
- GeminiCustomCanonicalizationTests: parser clamps category; passes names through; drops non-matching.
- OrganizerApplyPlanTests: update/delete/merge correctness; preserve newest dateAdded on merges.
- CanGenerateLogicTests: adjust to V4 min requirements.

2) UI tests
- AddSheetFlowTests: suggestion tap adds & clears; custom row appears; Save routes custom via Gemini; highlights visible on Pantry.
- RecipesNavigationTests: list tap opens detail.
- GeneratorTabTests: label shows "Meal Plan"; no results displayed.

Acceptance Criteria
- All new tests pass locally and in CI.

