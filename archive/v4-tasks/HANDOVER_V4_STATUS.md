# V4 Tasks — Status & Handover

Owner: Handover to next AI agent
Date: 2025-08-31
Scope: PRD_Scan_Pantry_V4.md and TASK_01…TASK_06 in V4 Tasks/

## TL;DR
- Build currently succeeds (Debug, iPhone 16 simulator)
- GeminiAPIService includes V4-required custom ingredient cleanup (strict category, descriptor preservation)
- Recipes list → detail navigation is supported via a convenience initializer
- Most Pantry Add-sheet UX and Pantry highlights wiring likely remain to be finished; tests not added yet

## Status by Task

- TASK 01 — Pantry Highlights (Scan + Manual): PARTIAL
  - markAsRecentlyAdded/isRecentlyAdded infra exists; UI highlight rendering in PantryView needs verification.
  - ResultsViewModel.addSelectedToPantry() should call pantryService.markAsRecentlyAdded(selectedIngredients) after save — verify/implement.
  - Auto-clear of highlight after ~5s is assumed present in service; ensure called on leaving Pantry tab.

- TASK 02 — Add Sheet: Immediate Add, Auto‑Clear, Custom + Gemini Cleanup: PARTIAL
  - Service side implemented: GeminiAPIService.canonicalizeCustomIngredients(names:allowedCategories:) with strict category validation and JSON-only parsing.
  - Pantry Add-sheet UI behaviors (instant add on suggestion tap, text auto-clear, "Add custom" row, Custom Inputs chips, ephemeral pill) need wiring in PantryView/PantryViewModel.
  - Save path must compose libraryItems + cleaned custom items, then call pantryService.addIngredients and markAsRecentlyAdded; handle Gemini failure fallback.

- TASK 03 — PantryService: Allow Duplicates on Save: UNKNOWN/LIKELY PENDING
  - Need to audit Services/PantryService.swift for any dedupe/merge on save and remove it (leave merging to Organizer).
  - Ensure dateAdded/purchaseDate set for new items.

- TASK 04 — Generator/Recipes fixes: PARTIAL
  - Recipes tab navigation: Added convenience init on GeneratedRecipeDetailView to accept Recipe directly, unblocking navigation.
  - Generator tab: verify removal of in-tab results, rename "Custom" → "Meal Plan", relax canGenerate rules to V3 minimal; adjust labels.
  - Ensure Favorites integrations remain intact.

- TASK 05 — Gemini Prompts: Scan Cleanup and Organizer: PARTIAL
  - Scan/custom canonicalization prompt strengthened (remove brand/size/qty/marketing; normalize simple pluralization; preserve descriptors; strict category list; JSON-only). Implemented in GeminiAPIService.
  - Organizer prompt (PantryOrganizerService) needs review to ensure strict category list, merge groups, drop non-food/gibberish, JSON-only plan.

- TASK 06 — Tests: NOT STARTED
  - Unit/UI tests listed in TASK_06_Tests.md are not present; need to add and ensure they pass locally/CI.

## Recent changes relevant to V4

- Services/GeminiAPIService.swift
  - Added canonicalizeCustomIngredients(names:allowedCategories:) and parser with strict category enforcement and input quality checks.
  - Implemented recipe detail generation helpers (optional for V4; can be leveraged later).
  - Cleaned response handling and error surfacing.

- Features/RecipeGenerator/GeneratedRecipeDetailView.swift
  - Added convenience initializer init(recipe: Recipe) → maps to RecipeUIModel; preserves difficulty, servings, time, description.
  - Enables existing navigation code that passes Recipe to compile and run.

## Open questions / decisions needed

1) PantryView highlight visuals: confirm the desired tint/halo style and ensure it does not conflict with expiration warnings.
2) Add-sheet UX: confirm copy/wording for "Add \"<query>\" as custom ingredient" row and the ephemeral pill text.
3) PantryService duplicates: confirm that all save-time merges are removed (Organizer is the only merge mechanism) and that acceptance criteria allow visible near-duplicates.
4) Generator canGenerate rules: specify minimal fields for Quick vs Meal Plan per V3 alignment.
5) Organizer prompt: confirm exact PantryCategory list usage and JSON schema for merge plan.

## Immediate next steps (suggested order)

1) TASK 01 — Wire highlight rendering in PantryView + Results integration call.
2) TASK 02 — Implement Add-sheet behaviors (instant add, auto-clear, custom row, chips); wire Save path to call Gemini cleanup and markAsRecentlyAdded; handle failure path.
3) TASK 03 — Remove dedupe/merge in PantryService add flows; verify dateAdded is set.
4) TASK 04 — Generator tab: rename labels to "Meal Plan", relax canGenerate; ensure no results render in this tab. Verify Recipes tab navigation still works.
5) TASK 05 — Review PantryOrganizerService prompt builder for V4 rules.
6) TASK 06 — Add tests per TASK_06_Tests.md and ensure all pass.

## How to run / validate

- Build (local):
  - xcodebuild -project IngredientScanner.xcodeproj -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build
- Run tests (once added):
  - xcodebuild -project IngredientScanner.xcodeproj -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' test
- API keys:
  - Ensure APIKeys.geminiAPIKey is set (GeminiAPIService will error if not configured).

## Risks / watchouts

- Allowing duplicates increases pantry clutter until Organizer runs — by design for V4.
- Gemini failures during custom cleanup must not block saving library picks; preserve customInputs for retry and show banner.
- Strict category enforcement means any category drift from prompts will drop items; ensure prompts list all valid PantryCategory.rawValue values.

## File pointers

- PRD: V4 Tasks/PRD_Scan_Pantry_V4.md
- Tasks Index: V4 Tasks/TASKS_README.md
- Service (scan/custom canonicalization): Services/GeminiAPIService.swift
- Pantry UI: Features/Pantry/* (PantryView.swift, PantryViewModel.swift)
- Results integration: Features/3_Results/ResultsViewModel.swift
- Pantry service: Services/PantryService.swift
- Recipes detail: Features/RecipeGenerator/GeneratedRecipeDetailView.swift
- Organizer Service: Services/PantryOrganizerService.swift
- Tests: Tests/* (to be added per TASK_06)

## Verification done

- Built successfully on iPhone 16 simulator (Debug) after recent changes.
- No test suite present for V4 items yet.

## Handover checklist

- [ ] Implement/verify TASK 01 UI + Results call
- [ ] Implement TASK 02 UI behaviors + save flow + failure banner
- [ ] Update PantryService to allow duplicates on save
- [ ] Generator tab label + enablement rules + remove inline results
- [ ] Organizer prompt review (PantryOrganizerService)
- [ ] Add Unit/UI tests per TASK 06; ensure green
- [ ] Smoke test: Scan → Results → Add → Pantry highlights visible; Pantry Add-sheet flow with custom inputs; Recipes navigation; Generator labeling/enabled state


