import SwiftUI

/// Swipeable, bounded week navigation wrapper for the Plans calendar.
/// - Supports horizontal swiping across weeks with a browse window:
///   back 3 weeks; forward limited to [today, today+7 days].
/// - Degrades animations to crossfades with Reduce Motion.
struct SwipeableMealPlanCalendarView: View {
    let plan: MealPlan
    var onSelectSlot: (Date, MealType, [MealSlot]) -> Void
    var onTapManage: () -> Void = {}

    @Environment(\.accessibilityReduceMotion) private var reduceMotion
    @State private var selectedIndex: Int = 0 // 0 = current week

    private let calendar = Calendar.current

    init(plan: MealPlan, onSelectSlot: @escaping (Date, MealType, [MealSlot]) -> Void, onTapManage: @escaping () -> Void = {}) {
        self.plan = plan
        self.onSelectSlot = onSelectSlot
        self.onTapManage = onTapManage
    }

    // MARK: - Date Bounds

    private var todayStart: Date { calendar.startOfDay(for: Date()) }
    private var forwardLimitDate: Date { calendar.date(byAdding: .day, value: 7, to: todayStart)! }
    private var backwardLimitDate: Date { calendar.date(byAdding: .day, value: -21, to: todayStart)! } // back 3 weeks

    // Allowed week indices relative to current week (0). Typically from -3 to max forward idx (0 or 1).
    private var allowedIndices: [Int] {
        // Compute base Monday of current week
        let baseWeek = WeeklyMealPlan(referenceDate: todayStart, calendar: calendar)
        // Determine if forward limit sits in current week (idx 0) or next week (idx 1)
        let nextWeekStart = calendar.date(byAdding: .day, value: 7, to: baseWeek.weekStart)!
        let forwardInNextWeek = forwardLimitDate >= nextWeekStart
        let maxForwardIndex = forwardInNextWeek ? 1 : 0
        let minIndex = -3
        return Array(minIndex...maxForwardIndex)
    }

    // Build a cached index of plan slots by date (yyyy-MM-dd) → meal → slots
    private var planIndex: [String: [MealType: [MealSlot]]] {
        indexPlanByDateAndMeal(plan: plan, calendar: calendar)
    }

    @State private var expandedWeeks: Set<Int> = [] // Track expanded states per week index

    var body: some View {
        let indices = allowedIndices
        let clampedSelection = min(max(selectedIndex, indices.first ?? 0), indices.last ?? 0)

        VStack(alignment: .leading, spacing: 16) {
            // Current page week title and arrow controls
            let currentWeek = week(for: clampedSelection)
            let title = WeeklyMealPlan.formatRange(currentWeek.weekStart, currentWeek.weekEnd, calendar: calendar)
            WeekIndicatorView(
                title: title,
                canGoPrevious: canGoPrevious,
                canGoNext: canGoNext,
                onPrevious: { withAnimation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion)) { goPrevious() } },
                onNext: { withAnimation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion)) { goNext() } }
            )
            .padding(.horizontal)
            .padding(.top, 8)

            TabView(selection: $selectedIndex) {
                ForEach(indices, id: \.self) { idx in
                    let week = week(for: idx)
                    VStack(spacing: 12) {
                        // Collapsed summary (tap to expand)
                        if !expandedWeeks.contains(idx) {
                            Button {
                                withAnimation(CalendarExpansionAnimator.animation(reduceMotion: reduceMotion)) {
                                    expandedWeeks.insert(idx)
                                }
                            } label: {
                                CollapsedCalendarView(
                                    weekDays: week.days,
                                    slotsProvider: { date, meal in
                                        guard isDateWithinBrowseWindow(date) else { return [] }
                                        let key = dayKey(for: date)
                                        let byMeal = planIndex[key] ?? [:]
                                        return byMeal[meal] ?? []
                                    }
                                )
                                .transition(CalendarExpansionAnimator.transition(reduceMotion: reduceMotion))
                            }
                            .buttonStyle(.plain)
                            .padding(.horizontal)
                        } else {
                            // Expanded full matrix with Manage button
                            ExpandedCalendarView(
                                weekDays: week.days,
                                slotsProvider: { date, meal in
                                    guard isDateWithinBrowseWindow(date) else { return [] }
                                    let key = dayKey(for: date)
                                    let byMeal = planIndex[key] ?? [:]
                                    return byMeal[meal] ?? []
                                },
                                favoritesProvider: { id in FavoritesStore.shared.isFavorite(id: id) },
                                onSelectSlot: { date, meal, slots in
                                    guard isDateWithinBrowseWindow(date) else { return }
                                    onSelectSlot(date, meal, slots)
                                },
                                onTapManage: onTapManage
                            )
                            .transition(CalendarExpansionAnimator.transition(reduceMotion: reduceMotion))

                            // Collapse control
                            Button {
                                withAnimation(CalendarExpansionAnimator.animation(reduceMotion: reduceMotion)) {
                                    expandedWeeks.remove(idx)
                                }
                            } label: {
                                Label("Collapse", systemImage: "chevron.up")
                                    .foregroundStyle(.secondary)
                                    .padding(.vertical, 4)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("Collapse week")
                        }
                        Spacer(minLength: 0)
                    }
                    .tag(idx)
                }
            }
            .tabViewStyle(.page(indexDisplayMode: .never))
            .animation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion), value: selectedIndex)
        }
        .onChange(of: selectedIndex) { newValue in
            // Clamp to allowed indices in case of programmatic scroll beyond bounds
            let indices = allowedIndices
            if newValue < (indices.first ?? 0) { selectedIndex = indices.first ?? 0 }
            if newValue > (indices.last ?? 0) { selectedIndex = indices.last ?? 0 }
        }
    }

    // MARK: - Helpers

    private var canGoPrevious: Bool {
        let minIdx = allowedIndices.first ?? 0
        return selectedIndex > minIdx
    }

    private var canGoNext: Bool {
        let maxIdx = allowedIndices.last ?? 0
        return selectedIndex < maxIdx
    }

    private func goPrevious() { if canGoPrevious { selectedIndex -= 1 } }
    private func goNext() { if canGoNext { selectedIndex += 1 } }

    private func week(for index: Int) -> WeeklyMealPlan {
        let baseWeek = WeeklyMealPlan(referenceDate: todayStart, calendar: calendar)
        if let ref = calendar.date(byAdding: .day, value: index * 7, to: baseWeek.weekStart) {
            return WeeklyMealPlan(referenceDate: ref, calendar: calendar)
        } else {
            return baseWeek
        }
    }

    private func dayKey(for date: Date) -> String {
        let d = calendar.startOfDay(for: date)
        let f = Self.yyyyMMdd
        return f.string(from: d)
    }

    private func isDateWithinBrowseWindow(_ date: Date) -> Bool {
        let d = calendar.startOfDay(for: date)
        return d >= backwardLimitDate && d <= forwardLimitDate
    }

    private func indexPlanByDateAndMeal(plan: MealPlan, calendar: Calendar) -> [String: [MealType: [MealSlot]]] {
        var result: [String: [MealType: [MealSlot]]] = [:]
        for day in plan.days {
            let key = Self.yyyyMMdd.string(from: calendar.startOfDay(for: day.date))
            for slot in day.meals {
                result[key, default: [:]][slot.mealType, default: []].append(slot)
            }
        }
        return result
    }

    private static let yyyyMMdd: DateFormatter = {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        f.locale = Locale(identifier: "en_US_POSIX")
        f.timeZone = TimeZone(secondsFromGMT: 0)
        return f
    }()
}

#Preview("SwipeableMealPlanCalendarView") {
    let plan = MealPlan(days: [])
    return SwipeableMealPlanCalendarView(plan: plan) { _, _, _ in }
}
