# V6 Engineering Guidelines — Recipe History & Enhanced Display

Purpose: Concise, enforceable rules distilled from `V6 Task/tasks.json` and PRD to guide implementation and reviews. Treat this as the single English reference for standards and guardrails.

## Scope & Principles

- Focus: Quick Results history, Plans calendar, Favorites; structured meal plan generation (Date × MealType slots).
- Priorities: Correctness of slot mapping → Performance → Accessibility → Polish.
- Separation: Quick ≠ Plans. Plans calendar must never display Quick results.

## Architecture & Data Model

- Structured Generation: Replace post-grouping with slot-based generation; every recipe carries exact (date, meal) assignment.
- Date Range: Valid window is [today, today + 7 days]. Same-day passed meals must be skipped automatically (defaults: Breakfast 10:30, Lunch 14:30, Dinner 21:30) and configurable via `MealCutoffManager`.
- Overlap Policy: On saving overlapping slots → replace non-favorited; skip favorited; show a summary banner; operation is atomic.
- Retention Policy (Plans): Keep last 4 calendar weeks visible; saving a 5th auto-removes the oldest week. Favorited items persist in Favorites.
- Storage Strategy (Quick): Keep up to 10 lightweight Quick items in memory; persist full data to disk with lazy loading; background, non-blocking saves; image caching with TTL.

## UX & Navigation

- Tabs: Segmented control inside Recipes page with tabs Quick | Plans | Favorites. Default tab = Quick.
- State: Each tab maintains its own state and scroll position across switches.
- Smart Contextual Jump (Quick only): Generate → inline toast preview with Good/Regenerate.
  - Good → save + navigate to Recipes > Quick (most recent at position 1).
  - Regenerate → dismiss toast; keep inputs.
- Plans Calendar: 7-day matrix (rows = Mon–Sun; columns = Breakfast, Lunch, Dinner). Swipe week-to-week (left = next, right = previous) within limits: back 3 weeks, forward to [today, today+7].
- Indicators: ● has plan, ○ no plan, ⭐ favorited, 🔄 recently regenerated.

## Capacity & Favorites

- Quick Capacity: 10 items max. No auto-eviction when full.
  - Header indicator: dots + counter, color feedback (<8 neutral, 8 yellow, 10 red) with Manage action.
  - When full (10/10), Good does not save; show notice + Manage action.
  - Insert new at position 1; shift others right.
- Favorites Semantics:
  - Heart button in detail views to add/remove favorites.
  - Favorites form a unified list across Quick + Plans; not affected by capacity/retention.
  - Protect favorites: Quick delete must not remove favorited items; Plans batch ops skip favorited slots and report summary.

## Performance Budgets

- Tab switching < 200 ms.
- Generator toast response < 100 ms.
- Memory usage < 50 MB for combined Quick (10) + Plans (4 weeks) + Favorites.
- Crash rate < 0.1% in three-tab features.
- Paging: Use virtual scrolling for horizontal pages > 5.
- Calendar: Lazy-load weeks; ensure smooth animated transitions; degrade to crossfades with Reduce Motion.

## Accessibility

- VoiceOver: Clear `accessibilityLabel` for all interactive elements, including capacity indicators and calendar cells.
- Dynamic Type: All empty states and cards must scale properly.
- Reduce Motion: All animations gracefully degrade to crossfades.
- Calendar Rotor: Provide rotor-friendly navigation for the matrix.

## Concurrency & Atomicity

- All multi-select batch operations are atomic (all succeed or all fail). Provide clear error aggregation when partial failures occur.
- Background persistence must be non-blocking to UI; use main-thread handoff for UI state updates.

## Coding Style & Patterns (Swift/SwiftUI)

- Follow existing SwiftUI architecture used in the app; do not invent new navigation paradigms.
- Prefer structs and immutable models; isolate side effects in services/managers.
- Use view models for UI state; keep views declarative and lightweight.
- File Placement: Create new files at the specified paths to keep module boundaries clean (Features/…, Views/…, Models/…, Services/…, Utils/…, Extensions/…).
- Error Handling: Fail fast in business logic; surface user-friendly notices for capacity/retention/overlap outcomes.
- Performance: Avoid heavy work in `body`; memoize computed subviews; prefer `@StateObject` for long-lived models.

## Testing Strategy

- Unit tests for all new managers/services, especially: MealPlanGeneration, DateRange/Cutoffs, QuickHistoryManager, Storage/Retention, BatchOps, Performance monitors.
- UI/Integration tests for: tab navigation performance, toast interactions, horizontal paging, calendar slot interactions, expand/collapse animations, accessibility flows.
- Visual regression for card layouts.

## Build & Validation Commands

- Build: `swift build` | `xcodebuild -scheme IngredientScanner build`
- Unit tests: `swift test` and targeted filters per component (see tasks.json).
- Xcode UI tests: `xcodebuild test -scheme IngredientScanner`
- Accessibility audit: Xcode Accessibility Inspector; verify Dynamic Type scaling and Reduce Motion behavior.
- Profiling: Instruments for memory and performance under load.

## Risks & Mitigations

- Tab complexity overwhelms users → Clear labels, iOS-consistent patterns, progressive disclosure; monitor tab usage distribution.
- Toast friction slows workflow → Keep response <100ms; clear Good/Regenerate choices; monitor selection rates.
- Calendar performance with larger datasets → Virtual scrolling, lazy loading, memory management; monitor memory and response times.
- Data migration from current structures → Versioned models and migration scripts; track migration integrity.

## PR Checklist (Definition of Done)

- Correct slot mapping: Each plan item has exact (date, meal) and respects date range + same-day cutoffs.
- Quick capacity rules honored; full-state UX shows notice + Manage.
- Favorites protection enforced in both Quick and Plans flows.
- Performance targets met (tab switch, toast, memory) with evidence (profiling/screenshots/metrics).
- Accessibility: VoiceOver labels, Dynamic Type, Reduce Motion coverage.
- Tests: New unit tests added; UI tests for critical flows; all passing locally.
- Documentation: Updated any feature-level READMEs and kept file/module structure consistent.

---
Source references: `V6 Task/tasks.json`, `V6 Task/recipe_history_and_optimization_prd.md`.

