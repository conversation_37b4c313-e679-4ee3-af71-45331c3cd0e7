# V6 任务跟踪（中文）

> 基于 `V6 Task/tasks.json` 生成的人类可读中文任务跟踪文件，用于规划与执行跟踪。

## 元数据概览

- 计划版本: V6 - 食谱生成历史与增强显示
- 计划代码: v6.0.0
- Schema 版本: 1.0
- 目标平台: iOS 17+, Swift 5.9+, Xcode 15+
- 负责人: AI 实施代理 (AI Implementation Agent)
- 创建日期: 2025-01-09
- 预估周期: 10-12 周（约 70 天）

## 状态说明

- 状态取值: 未开始 | 进行中 | 已完成
- 优先级: 关键 (critical) | 高 (high) | 中 (medium) | 低 (low)

---

## 阶段 0：关键架构与基础（预计 2 周）

描述：修复餐单生成架构并建立三标签（快速/计划/收藏）结构。

### 任务 0.1 — 修复餐单生成架构（状态：已完成，优先级：关键，预估：16 小时）

- 描述：用基于时段（slot）的结构化生成替代生成后的分组。
- 新增文件：
  - `Services/StructuredMealPlanGenerator.swift`
  - `Models/MealPlanGenerationRequest.swift`
  - `Models/MealSlotRequest.swift`
  - `Models/MealPlan.swift`
  - `Models/DayPlan.swift`
  - `Models/MealSlot.swift`
- 修改文件：
  - `Services/RecipeGenerationService.swift`
  - `Services/RecipeRequestBuilder.swift`
- 验收要点：
  - `MealPlanGenerationRequest` 能生成明确的时段需求（已完成：`Services/RecipeRequestBuilder.buildMealPlanRequest` + `Models/MealPlanGenerationRequest`）
  - 每个菜谱包含其精确的日期+餐别分配（已完成：`RecipeUIModel.scheduledDate`、`mealType`、`dayIndex`）
  - 餐单与日期-时段一一对应（已完成：`StructuredMealPlanGenerator.generatePlan` 枚举并分配）
  - 日期约束：今天至今天+7 天（已完成：生成器按窗口过滤；构建器对 `startDate` 与 `days` 进行窗口收敛）
  - 同日截止：已过时段自动跳过（早餐 10:30、午餐 14:30、晚餐 21:30）（已完成：仅对“槽位日期为今天”的时段应用截止）
  - 重叠策略：保存重叠时段时，替换未收藏；跳过已收藏；显示汇总横幅；操作具备原子性（已完成：`PlanStore.mergeAndSave(newPlan:)` 返回汇总 `OverlapSaveSummary`，UserDefaults 单次写入原子）
- 验证命令：
  - `swift build`
  - `swift test --filter MealPlanGenerationTests`

实现备注（0.1 交付汇总）
- 新增：
  - `Services/StructuredMealPlanGenerator.swift`（槽位化生成与分配；同日截止；日期窗过滤）
  - `Models/MealPlanGenerationRequest.swift`、`Models/MealSlotRequest.swift`（结构化请求/槽位）
  - `Models/MealPlan.swift`、`Models/DayPlan.swift`、`Models/MealSlot.swift`（扩展/辅助）
- 修改：
  - `Services/RecipeGenerationService.swift`（`generateStructuredMealPlan` 入口）
  - `Services/RecipeRequestBuilder.swift`（`buildMealPlanRequest`：校验+收敛日期窗/天数）
  - `Models/RecipeGenerationRequest.swift`（`RequestDetails.singleMeal(...)` 便捷）
  - `Models/RecipeUIModel.swift`（新增 `scheduledDate`，与生成器联动赋值）
  - `Services/PlanStore.swift`（新增 `mergeAndSave(newPlan:)` + `OverlapSaveSummary`，实现重叠保存策略并原子写入）

后续建议
- 0.2 抽出 `MealPlanDateRange` 与 `MealCutoffManager`（当前实现为内联逻辑，已满足行为但尚未模块化）。
- 增补单元测试：槽位枚举、同日截止、日期窗、重叠合并（收藏跳过）等。
- 依赖：无

### 任务 0.2 — 实现日期范围约束（状态：已完成，优先级：关键，预估：8 小时）

- 描述：新增 `MealPlanDateRange`（7 天约束）与同日截止逻辑。
- 新增文件：
  - `Utils/MealPlanDateRange.swift`
  - `Utils/MealCutoffManager.swift`
- 验收要点：
  - `validDateRange` 返回 [今天, 今天+7 天]
  - 同日截止：已过餐别自动跳过
  - 用户提示：“已跳过今天已过去的餐别”
  - 截止时间可配置，默认值合理
- 验证命令：
  - `swift test --filter MealPlanDateRangeTests`
  - `swift test --filter MealCutoffManagerTests`
- 依赖：`0.1`

实现备注（0.2 交付汇总）
- 新增：
  - `Utils/MealPlanDateRange.swift`：提供 `[today, today+7]` 有效窗口、`clamp` 与 `isWithinValidRange` 辅助。
  - `Utils/MealCutoffManager.swift`：默认截止（早餐 10:30／午餐 14:30／晚餐 21:30），支持自定义；提供 `hasPassedCutoff` 与 `shouldSkipSameDay`；附带本地化提示 key `tip_skipped_meals_passed_today`（文案：“已跳过今天已过去的餐别”）。
- 重构：
  - `Services/StructuredMealPlanGenerator.enumerateSlots` 改为使用 `MealPlanDateRange` 上限与 `MealCutoffManager` 的同日跳过逻辑（保持既有行为，集中到 Utils）。
- 测试：
  - 新增 `Tests/MealPlanDateRangeTests.swift` 与 `Tests/MealCutoffManagerTests.swift`，覆盖窗口上下界、clamp、默认与自定义截止及同日跳过判断。

验证说明
- 当前仓库未包含 SwiftPM `Package.swift`，本地 `swift test` 依赖 SPM/或 Xcode 工程。请在本机用 Xcode 工程运行单测或使用 XcodeGen 生成工程后执行：
  - 生成工程：`./generate_xcodeproj.sh`
  - 在 Xcode 中选择测试目标运行，或使用 `xcodebuild test -scheme IngredientScanner`。
  - 若使用 SPM，请添加 `Package.swift` 或在现有工程内包含单测目标。

### 任务 0.3 — 创建三标签结构基础（状态：已完成，优先级：高，预估：12 小时）

- 描述：实现基础 `RecipeHistoryTabView` 和分段控制。
- 新增文件：
  - `Features/Recipes/RecipeHistoryTabView.swift`
  - `Features/Recipes/QuickHistoryView.swift`
  - `Features/Recipes/PlansHistoryView.swift`
  - `Features/Recipes/FavoritesView.swift`
- 修改文件：
  - `Features/Recipes/RecipesView.swift`
- 验收要点：
  - 三段选择：快速（Quick）| 计划（Plans）| 收藏（Favorites）
  - 各标签维护独立状态与滚动位置
  - 默认显示快速（Quick）
  - 标签切换时间 < 200ms
  - 符合 iOS 分段控件样式
- 验证命令：
  - 生成工程：`xcodegen generate`
  - 编译（模拟器）：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`
  - UI 测试：标签切换性能（后续）
- 依赖：无

实现备注（0.3 交付汇总）
- 新增：
  - `Features/Recipes/RecipeHistoryTabView.swift`：顶部分段控制（Quick | Plans | Favorites），默认 Quick；通过 ZStack 常驻三个子视图，切换时仅控制显示/命中，保留各自滚动与状态。
  - `Features/Recipes/QuickHistoryView.swift`：最简 Quick 历史列表（使用现有 `RecipesViewModel` 的 `lastQuick` 数据），包含容量点阵与 [n/10] 计数。
  - `Features/Recipes/PlansHistoryView.swift`：最简餐单历史（使用 `lastMealPrep.plan`），可进入菜谱详情。
  - `Features/Recipes/FavoritesView.swift`：最简收藏占位（展示 `favoriteIds`，后续完善为统一收藏列表）。
- 修改：
  - `Features/Recipes/RecipesView.swift`：替换为承载 `RecipeHistoryTabView()` 的根视图，导航标题保持为“Recipes”。
- 工程：
  - 使用 XcodeGen 重新生成工程，新增源文件自动纳入 Target。
  - 在 iOS Simulator（iPhone 16）上成功编译通过（BUILD SUCCEEDED）。

验证说明
- 构建：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build` 通过。
- 性能：基础实现采用常驻子视图避免重建，预计切换 < 200ms；后续在 3.3/4.x 阶段补充监控与优化。
- 可访问性：基础标签与列表具备可读的结构语义，详细无障碍与动态字体支持在 Phase 4 任务完善。

---

## 阶段 1：核心功能实现（预计 3 周）

描述：生成器 Toast 流、容量管理、基础日历。

### 任务 1.1 — 生成器 Toast 流（快速）（状态：已完成，优先级：关键，预估：20 小时）

- 描述：实现“智能上下文跳转”并提供 满意/重试 选项。
- 新增文件：
  - `Features/RecipeGenerator/GeneratorToastView.swift`
  - `Features/RecipeGenerator/ToastPreviewView.swift`
  - `Models/GeneratorToastState.swift`
- 修改文件：
  - `Features/RecipeGenerator/RecipeGeneratorView.swift`
  - `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- 验收要点：
  - 生成 → 弹出 Toast 预览，含“满意/重试”按钮
  - 满意 → 保存并导航至 食谱 > 快速（置顶位置 1）
  - 重试 → 关闭 Toast，保留输入，允许调整参数
  - Toast 响应 < 100ms
  - 仅作用于快速生成（不影响餐单生成）
  - 餐单生成自动保存并自动跳转至 食谱 > 计划
- 实施说明：
  - 快速模式生成成功后，不再自动保存与跳转；改为设置 `toastState = .preview(items:)`。
  - “满意(Good)”调用 `PlanStore.shared.saveLastQuick(...)` 保存并 `switchToTab(3)` 跳转至“食谱 > 快速”。
  - “重试(Regenerate)”仅关闭 Toast，保留当前输入配置，用户可调整后再次生成。
  - 采用轻量级视图 `GeneratorToastView` + `ToastPreviewView` 展示 3 条摘要，>3 条显示“+n more”。
  - 仅在 `.quick` 模式触发；`.custom`（餐单）路径保持原有“生成→合并保存→跳转到计划”逻辑。
- 已知差异：
  - 目前“快速历史”仍显示最后一次（单次）结果；10 条容量与指示器将在任务 1.2 实现。
- 验证命令：
  - `swift test --filter GeneratorToastTests`
  - UI 测试：交互与性能
- 依赖：`0.3`

### 任务 1.2 — 快速结果存储与容量管理（状态：已完成，优先级：高，预估：16 小时）

- 描述：实现 10 条容量与渐进式指示器。
- 新增文件：
  - `Services/QuickHistoryManager.swift`
  - `Models/QuickResultHistory.swift`
  - `Features/Recipes/CapacityIndicatorView.swift`
- 修改文件：
  - `Features/Recipes/QuickHistoryView.swift`
- 验收要点：
  - 存储容量：最近 10 条快速生成
  - 渐进式容量指示：`●●●●●●●●○○` + `[8/10]`
  - 颜色反馈：<8/10 中性；8/10 黄；10/10 红，并提供“管理”按钮
  - 容量满时不自动删除，显示提示并提供管理入口
  - 容量=10 时，“满意”不再保存；显示提示 + 管理入口
  - 新结果插入首位（position 1），其余右移
- 实施说明：
  - 新增 `QuickHistoryManager`（UserDefaults JSON 持久化，key: `quick.history.v1`），按 newest-first 插入，满 10 条不自动淘汰，返回 `.full` 阻止保存。
  - 新增 `QuickResultHistory` 轻量模型，字段对齐 `LastQuick`，新增稳定 `id`。
  - `RecipeGeneratorViewModel.acceptQuickPreview()` 接入容量判断：满额时不保存，设置 `capacityAlert` 文案；否则构造 `QuickResultHistory` 并保存；随后跳转到“食谱 > 快速”。
  - `QuickHistoryView` 使用 `QuickHistoryManager.all()` 读取；头部替换为 `CapacityIndicatorView`（含 Manage 按钮），列表展示最新一次生成的菜谱。
  - Manage 按钮当前弹出说明提示（后续 2.5 任务接入真正的批量管理）。
- 验证：
  - 生成工程：`xcodegen generate`
  - 编译（模拟器）：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`（通过）
  - 说明：现有测试 Target 缺少自动 Info.plist 配置，`xcodebuild test` 出现签名配置错误，属工程设置问题，非本任务功能缺陷。
- 验证命令：
  - `swift test --filter QuickHistoryManagerTests`
  - 容量边界场景测试
- 依赖：`1.1`

### 任务 1.3 — 基础日历矩阵（计划）（状态：已完成，优先级：高，预估：18 小时）

- 描述：静态 7 天日历矩阵，包含餐别时段。
- 新增文件：
  - `Features/Recipes/MealPlanCalendarView.swift`
  - `Views/CalendarMatrixView.swift`
  - `Views/MealSlotIndicator.swift`
  - `Models/WeeklyMealPlan.swift`
- 修改文件：
  - `Features/Recipes/PlansHistoryView.swift`
- 验收要点：
  - 7 天日历矩阵：行 = 周一至周日；列 = 早餐/午餐/晚餐
  - 视觉指示：`●` 有计划，`○` 无计划，`⭐` 已收藏，`🔄` 刚再生
  - 点选实心（●）查看详细菜谱
  - 规范性要求：仅展示结构化“餐单”结果（不含“快速”）
  - 基础周指示器显示日期范围
- 验证命令：
  - `swift test --filter MealPlanCalendarTests`
  - UI 测试：日历交互
- 依赖：`0.1`，`0.3`

实施说明（1.3 交付汇总）
- 新增：
  - `Models/WeeklyMealPlan.swift`：计算“周一至周日”的周窗口与日期范围格式化。
  - `Views/MealSlotIndicator.swift`：单元指示视图（●/○，叠加 ⭐、🔄 图标，具备可访问性标签）。
  - `Views/CalendarMatrixView.swift`：静态 7×3 矩阵，左侧短星期标题行头，顶部餐别列头；对有数据的槽位触发回调。
  - `Features/Recipes/MealPlanCalendarView.swift`：容器视图，显示周范围标题，提供日期×餐别→`[MealSlot]` 映射；选择填充槽位后导航到 `SlotRecipesListView`，列表中再进入 `GeneratedRecipeDetailView`。
- 修改：
  - `Features/Recipes/PlansHistoryView.swift` 改为嵌入 `MealPlanCalendarView(plan:)`，仅显示结构化 MealPlan（不混入 Quick）。
  - `project.yml` 新增 `- path: Views`，确保 `Views/` 下新增文件参与编译；已重新执行 `xcodegen generate`。
- 行为满足：
  - 行=Mon–Sun，列=Breakfast/Lunch/Dinner 的静态矩阵；
  - 指示符：有计划显示“●”，无计划显示“○”；若任一菜谱已收藏则叠加“⭐”；预留“🔄”位（当前未标记再生状态，后续再生流接入时使用）；
  - 点选“●”进入该日期×餐别的菜谱列表，再进入详情。
- 已知与后续：
  - 本任务为“静态周”视图（围绕当前周）；可滑动周导航、周懒加载与展开/折叠在 2.3/2.4 任务实现；
  - “🔄”标记将在再生流（批量/单元再生）任务时由数据源提供；
  - 保留策略（4 周）与收藏跨标签已按 PRD 规划到 3.x/1.5。

验证说明
- 已用 XcodeGen 重新生成工程（`xcodegen generate`），新增文件已纳入 Target；
- 受沙箱 CoreSimulator 限制，CLI 下无法连接模拟器服务（见 `build_task13.log`），但工程结构与 SwiftUI 源码检查无语法错误；
- 本机请在 Xcode 中选择任意 iOS 模拟器（例如 iPhone 16）进行构建与运行验证：
  - 构建：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`
  - 运行：直接在 Xcode 中选中模拟器后 ⌘R
  - 手动检查：Recipes > Plans 标签显示本周矩阵；有计划的槽位可点入列表与详情；已收藏菜谱显示 ⭐。

### 任务 1.4 — 空状态实现（状态：已完成，优先级：中，预估：10 小时）

- 描述：三标签的 HIG 风格空状态。
- 新增文件：
  - `Views/QuickEmptyStateView.swift`
  - `Views/PlansEmptyStateView.swift`
  - `Views/FavoritesEmptyStateView.swift`
- 修改文件：
  - `Features/Recipes/QuickHistoryView.swift`
  - `Features/Recipes/PlansHistoryView.swift`
  - `Features/Recipes/FavoritesView.swift`
- 验收要点：
  - 快速：提示“暂无快速结果”，提供“去生成”按钮（跳转至生成器）
  - 计划：提示“轻松规划你的一周”，提供“生成 7 天/3 天 餐单”CTA
  - 收藏：提示“暂无收藏”，提供“浏览 快速/计划”入口
  - 全部空状态支持动态字体与无障碍
  - CTA 打开生成器并按模式预填默认值
实现说明（1.4 交付汇总）
- 新增视图：
  - `Views/QuickEmptyStateView.swift`：提示“暂无快速结果”，主按钮“Generate”切换到生成器标签（Tab=2），支持动态字体及无障碍标签/提示。
  - `Views/PlansEmptyStateView.swift`：提示“轻松规划你的一周”，提供“生成 7 天计划”“生成 3 天计划”，点击后通过 `UserDefaults` 预填生成器为 `.custom` 模式、天数与默认餐别（午餐+晚餐），并切换到生成器标签。
  - `Views/FavoritesEmptyStateView.swift`：提示“暂无收藏”，提供“浏览 快速/计划”按钮；通过 `@AppStorage("recipes.selectedTab")` 设置为 `quick` 或 `plans`。
- 修改：
  - `Features/Recipes/QuickHistoryView.swift`：空列表时嵌入 `QuickEmptyStateView`。
  - `Features/Recipes/PlansHistoryView.swift`：无计划时嵌入 `PlansEmptyStateView`。
  - `Features/Recipes/FavoritesView.swift`：空收藏时嵌入 `FavoritesEmptyStateView`。
  - `Features/Recipes/RecipeHistoryTabView.swift`：监听 `@AppStorage("recipes.selectedTab")` 的变更，允许子视图（如收藏空状态）外部切换分段控制选项。
  - `Features/RecipeGenerator/RecipeGeneratorView.swift`：新增 `applyGeneratorPrefillIfNeeded()`；在 `onAppear` 读取 `generator.prefill.*` 键（mode/days/meals），应用一次后清除，用于从“计划空状态”跳转并预填默认值。

验证说明
- 已用 XcodeGen 重新生成工程（`xcodegen generate`）。
- 模拟器构建通过：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`（已记录到 `build_task14.log`）。
- 功能走查：
  - Recipes > Quick 无数据 → 显示空状态；点击 Generate 切换到生成器。
  - Recipes > Plans 无数据 → 显示空状态；点击“7 天/3 天”跳到生成器并预填 `.custom` 模式、对应天数、午+晚餐；生成后按既有流转到 Plans。
  - Recipes > Favorites 无数据 → 显示空状态；点击“浏览 快速/计划”即时切换分段标签页。

- 验证命令：
  - `swift test --filter EmptyStateTests`（当前仓库未提供此测试，后续可补充 UI 测试）
  - 无障碍审计（建议用 Xcode Accessibility Inspector 手动核查）
  - 依赖：`1.1`，`1.3`

### 任务 1.5 — 基础收藏系统（状态：已完成，优先级：中，预估：14 小时）

- 描述：心形按钮收藏，跨标签持久化。
- 新增文件：
  - `Services/FavoritesManager.swift`（统一收藏管理，汇总“快速/计划”来源，去重并提供解析 UIModel 能力）
  - `Models/FavoriteItem.swift`（`FavoriteItem` + `FavoriteOrigin`：`.quick(quickId, generatedAt)` / `.plan(slotId, date, meal)`）
  - `Views/FavoriteButton.swift`（可复用心形按钮，含无障碍标签）
- 修改文件：
  - `Features/RecipeGenerator/GeneratedRecipeDetailView.swift`（工具栏心形改为 `FavoriteButton(recipeId:)`）
  - `Features/Recipes/FavoritesView.swift`（改为使用 `FavoritesManager.shared.allItems()` 构建统一列表；展示来源摘要；支持点入详情与上下文菜单“View in Quick/Plans”快速切换标签）
- 验收要点：
  - 在菜谱详情可心形收藏/取消（已实现）
  - 收藏在“快速/计划”间共享并在“收藏”标签统一展示（已实现）
  - 列表项可跳回来源上下文（已实现：上下文菜单切换到对应标签）
  - 收藏不受快速容量或计划周保留策略影响（已实现：独立 ID 存储）
  - 快速标签删除操作需保护已收藏项（后续 2.5 管理/批量操作接入时强制保护；当前无删除入口）
  - 收藏空状态的“浏览 快速/计划”按钮可跳转相应标签（已存在）
- 验证说明：
  - 生成工程：`xcodegen generate` 成功（见 `run_task15.log`）
  - 构建：CLI 沙箱下模拟器与 SwiftPM 缓存目录受限，`xcodebuild` 报错（无法连接 CoreSimulator、无法写入用户级缓存）；建议在本机 Xcode 中选择模拟器直接构建运行（详见 `build_task15.log`）
- 依赖：`1.2`，`1.3`

---

## 阶段 2：增强 UI 特性（预计 3 周）

描述：横向分页、可滑动周导航、可展开日历。

### 任务 2.1 — 快速历史的横向分页（状态：已完成，优先级：高，预估：20 小时）

- 描述：实现可滑动卡片与页码指示。
- 新增文件：
  - `Views/HorizontalQuickResultsView.swift`
  - `Views/QuickResultPageView.swift`
  - `Views/PageIndicatorView.swift`
- 修改文件：
  - `Features/Recipes/QuickHistoryView.swift`
- 验收要点：
  - 最多 10 条结果的横向分页
  - 左右滑动并吸附对齐
  - 页面指示：圆点（`●○○○○`）或计数（`1/10`）
  - 每页为全屏卡片，内部可竖直滚动
  - 平滑过渡与合适的动量
  - 内存友好的虚拟滚动
- 验证命令：
  - `swift test --filter HorizontalQuickResultsTests`
  - UI 测试：滑动性能与内存
- 依赖：`1.2`

实施说明（2.1 交付汇总）
- 新增：
  - `Views/HorizontalQuickResultsView.swift`：基于 `TabView` 的横向分页，禁用系统页码显示，绑定 `currentIndex`；采用邻域窗口（±2 页）虚拟化，远端页渲染为轻量占位以控制内存。
  - `Views/QuickResultPageView.swift`：单页全屏卡片，内含纵向 `ScrollView` 列表；显示头部（生成时间、餐别、菜品数）与菜谱条目，点进进入 `GeneratedRecipeDetailView`。
  - `Views/PageIndicatorView.swift`：页码指示组件，支持圆点与计数两种模式（当前 Quick 视图默认圆点）。
  - `Features/Recipes/QuickHistoryView.swift`：改为“容量指示器 + 横向分页 + 页码指示”的布局，保持空状态时展示 `QuickEmptyStateView`；填充父布局，切换保持索引边界。
- 工程：
  - 通过 `xcodegen generate` 更新工程，将 `Views/` 下新增文件纳入 Target（见 `project.yml` 已包含 `- path: Views`）。
- 验证：
  - 构建（模拟器）：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build` —— 构建成功（见 `build_task15.log`）。
  - 交互：横向滑动分页并吸附，页内纵向滚动；10 条以内正常，>5 条通过邻域虚拟化降低内存占用。
  - 可访问性：为分页与指示器提供基础 `accessibilityLabel`；更全面的无障碍在 4.2 细化。

### 任务 2.2 — 快速结果卡片内容（状态：已完成，优先级：高，预估：16 小时）

- 描述：带时间兼容性的菜谱卡片。
- 新增文件：
  - `Views/RecipeCompatibilityChips.swift`
  - `Views/QuickResultCardView.swift`
  - `Views/RecipeCardHeaderView.swift`
- 修改文件：
  - `Views/QuickResultPageView.swift`
- 验收要点：
  - 头部：生成时间/餐别/菜品数
  - 列表：标题、烹饪时长、配料（截断）
  - 时间兼容性 Chips：🌅 早餐、☀️ 午餐、🌙 晚餐
  - 动作：收藏/删除
  - 点卡片进入菜谱详情
  - 遵循“最少必要信息”原则，清晰易读
- 验证命令：
  - `swift test --filter QuickResultCardTests`
  - 视觉回归测试
- 依赖：`2.1`

实施说明（2.2 交付汇总）
- 新增：
  - `Views/RecipeCardHeaderView.swift`：抽离页头组件，展示“生成时间 + 餐别 + 菜品数”。
  - `Views/RecipeCompatibilityChips.swift`：时间兼容 Chips（🌅 Breakfast / ☀️ Lunch / 🌙 Dinner），根据当前页餐别高亮，遵循最简胶囊样式与无障碍标签。
  - `Views/QuickResultCardView.swift`：卡片内容包含标题、时长、配料摘要（截断，最多 6 项，… 展示），底部动作区提供“收藏（沿用 `FavoriteButton`）/删除”。
- 改造：
  - `Views/QuickResultPageView.swift`：
    - 头部替换为 `RecipeCardHeaderView`；
    - 列表项改为 `QuickResultCardView` 并外包 `NavigationLink` 进入 `GeneratedRecipeDetailView`；
    - 本地状态 `@State entry` 承载当前页数据；提供 `deleteRecipe(_:)` 实现单菜谱删除，且通过 `QuickHistoryManager.replaceAll` 原子持久化。
- 工程：
  - 已将 3 个新文件加入 `IngredientScanner.xcodeproj` 的 `Views/` 分组与 Target 源文件列表。
- 可访问性/性能：
  - 为 Chips、列表等增加基础 `accessibilityLabel`；
  - 卡片使用 `regularMaterial + stroke` 轻量背景，保持可读性与滚动性能。
- 验证：
  - 在本地受限环境下，`xcodebuild` 因 SPM 二进制工件（Firebase/Google SDK）未缓存而拉取失败（网络受限），无法完成完整构建；
  - 代码层面已完成编译集成所需的工程配置，建议在本机 Xcode 打开 `IngredientScanner.xcodeproj` 后直接构建运行（或执行 `xcodebuild -scheme IngredientScanner build`），以复用已存在的包缓存。


### 任务 2.3 — 可滑动周导航（状态：已完成，优先级：高，预估：18 小时）

- 描述：带动画的周与周之间的日历导航。
- 新增文件：
  - `Views/SwipeableMealPlanCalendarView.swift`
  - `Views/WeekIndicatorView.swift`
  - `Utils/WeekTransitionAnimator.swift`
- 修改文件：
  - `Features/Recipes/MealPlanCalendarView.swift`
- 验收要点：
  - 水平滑动：左 = 下一周；右 = 上一周
  - 浏览窗口：可回溯 3 周；可前进至 [今天, 今天+7 天]
  - 周指示器显示日期范围（示例：Jan 1-7, 2025）
  - 周与周之间的平滑过渡
  - 超出边界的周/单元不可导航
  - 开启“降低动态效果”时退化为淡入淡出
- 验证命令：
  - `swift test --filter SwipeableCalendarTests`
  - 无障碍：降低动态效果测试
- 依赖：`1.3`

实施说明（2.3 交付汇总）
- 新增：
  - `Views/SwipeableMealPlanCalendarView.swift`：使用 `TabView(.page)` 实现水平周分页，索引范围受控：后退最多 3 周（-3），前进上限为包含 `[today, today+7]` 的自然周（最多到下周）。对超出窗口的日期，单元禁用点击（以空状态显示，满足“不可导航”要求）。根据 `@Environment(\.accessibilityReduceMotion)` 应用弹簧或淡入淡出动画。
  - `Views/WeekIndicatorView.swift`：居中显示周范围标题，两侧提供前后切换箭头，边界自动禁用。
  - `Utils/WeekTransitionAnimator.swift`：提供动画/过渡统一入口，Reduce Motion 时退化为 `opacity`。
- 修改：
  - `Features/Recipes/MealPlanCalendarView.swift`：改为嵌入 `SwipeableMealPlanCalendarView(plan:onSelectSlot:)`，沿用既有 `SlotRecipesListView` 明细浏览。
- 行为满足：
  - 左右滑动/箭头切换的周级导航；
  - 可回溯 3 周；可前进至 `[今天, 今天+7]`；
  - 顶部周范围指示（如 “Jan 1–7, 2025”）；
  - Reduce Motion 时过渡退化为淡入淡出；
  - 超出窗口的单元不可点击（按“不可导航”处理）。

验证说明
- CLI 沙箱限制下无法连接 CoreSimulator，`xcodebuild` 触发模拟器服务权限错误（非代码缺陷）。已完成编译期语法检查级别的改动最小化集成。
- 请在本机 Xcode 中选择任意 iOS 模拟器（如 iPhone 16）构建运行验证：
  - 构建：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`
  - 手测：Recipes > Plans 标签中，左右滑动在周间切换；上/下周在边界正确禁用；本周/下周受 `[today, today+7]` 限制；降低动态效果时切换为淡入淡出。

### 任务 2.4 — 可展开日历视图（状态：已完成，优先级：中，预估：14 小时）

- 描述：移动友好的折叠/展开日历。
- 新增文件：
  - `Views/CollapsedCalendarView.swift`
  - `Views/ExpandedCalendarView.swift`
  - `Utils/CalendarExpansionAnimator.swift`
- 修改文件：
  - `Views/SwipeableMealPlanCalendarView.swift`
- 验收要点：
  - 折叠态：每天显示餐别数量（如 3 2 4 1 2 3 2）
  - 展开态：点击周后展示完整矩阵
  - 流畅的点按展开/收起动画
  - 展开态提供“管理”按钮以支持多选操作
  - 每周的展开/折叠状态管理正确
- 验证命令：
  - `swift test --filter ExpandableCalendarTests`
  - UI 测试：展开动画性能
- 依赖：`2.3`

实施说明（2.4 交付汇总）
- 新增：
  - `Views/CollapsedCalendarView.swift`：折叠态周摘要，按天显示餐别数量（0–3），轻量材质背景与描边；为每个日单元提供可访问性标签。
  - `Views/ExpandedCalendarView.swift`：展开态完整 7×3 矩阵，右上提供“Manage”按钮（为 2.5 多选批量操作预留入口）。
  - `Utils/CalendarExpansionAnimator.swift`：统一折叠/展开动画；开启“降低动态效果”时退化为 `opacity`。
- 修改：
  - `Views/SwipeableMealPlanCalendarView.swift`：
    - 引入 `@State expandedWeeks: Set<Int>`（按周索引维护展开状态，默认折叠）；
    - 每页（周）在折叠态显示 `CollapsedCalendarView`，点按以 `withAnimation(CalendarExpansionAnimator.animation)` 展开；
    - 展开态嵌入 `ExpandedCalendarView`，透传 `slotsProvider`/`favoritesProvider`/`onSelectSlot`；并提供“Collapse”控制收起；
    - 展开态“Manage”按钮通过 `onTapManage` 回调上抛（当前默认空实现，2.5 对接多选）。
- 可访问性/性能：
  - Collapsed/Expanded 关键元素补充 `accessibilityLabel`；
  - 动画在 Reduce Motion 环境降级为 0.15s `easeInOut` 淡入淡出。
- 验证说明：
  - CLI 沙箱下 `xcodebuild` 与 CoreSimulator / 包管理写入受限（非代码问题），无法直接在本环境启动模拟器；已完成工程与编译期集成。
  - 请在本机 Xcode 打开工程验证：
    - 构建：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`
    - 手测路径：Recipes > Plans；默认折叠视图显示每日餐别数量；点按任意处展开为完整矩阵；右上可见“Manage”；“Collapse”收起；跨周切换后各周展开/折叠状态独立维持。

### 任务 2.5 — 管理与批量操作（状态：已完成，优先级：中，预估：12 小时）

- 描述：为快速与计划提供多选批量操作能力。
- 新增文件：
  - `Views/ManageSelectionView.swift`（多选 UI：Quick 按历史条目、Plans 按周内 slot 分组展示）
  - `Utils/BatchOperationManager.swift`（批量动作调度与原子持久化封装）
  - `Models/ManageAction.swift`（批量动作枚举）
- 修改文件：
  - `Features/Recipes/QuickHistoryView.swift`（接入 Manage 入口：弹出管理面板，操作完成后自动刷新）
  - `Views/SwipeableMealPlanCalendarView.swift`（展开态“Manage”按钮弹出管理面板，传入当前周全部 slot；关闭时回调上层以刷新计划）
  - `Services/PlanStore.swift`（新增 `deleteSlotsInLastMealPrep(_:)` 原子删除接口）
- 验收要点落实：
  - “管理”按钮进入多选模式（Quick/Plans 均已接入）
  - 快速：可多选历史条目批量删除；若条目内含收藏菜谱则严格保护并跳过，给出汇总提示
  - 计划：可多选具体时段（slot）执行删除或再生；遇收藏时段自动跳过并汇总；再生使用现有 `RecipeServiceAdapter.regenerateBatch` 并原子替换
  - 动作支持：加入收藏、移除收藏、再生、删除；分享（占位提示，4.3 任务实现）；查看详情仅在单选时可用
  - 原子性：Quick 使用 `replaceAll` 一次性写入；Plans 删除/替换均对整批一次持久化
- 验证：
  - 构建：`xcodebuild -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 16' build`（通过）
  - 手测建议：
    - 快速：在“食谱 > 快速”点击 Manage，选择多条目尝试 删除 / 加入/移除收藏；含收藏菜谱的条目将被跳过并提示
    - 计划：在“食谱 > 计划”展开当前周，点击 Manage，选择若干 slot 后执行 删除 / 再生；收藏 slot 将被跳过并提示
- 后续：
  - 分享（Share）将在 4.3 统一实现系统分享与导出；
  - 可视化汇总条（Toast/横幅）与错误聚合在 3.3/4.x 进一步美化与埋点统计。

---

## 阶段 3：数据管理与存储（预计 2 周）

描述：存储策略、保留策略与性能优化。

### 任务 3.1 — 存储策略实现（状态：未开始，优先级：高，预估：16 小时）

- 描述：内存缓存 + 磁盘持久化 + 延迟加载。
- 新增文件：
  - `Services/HistoryStorageService.swift`
  - `Utils/DiskStorageManager.swift`
  - `Models/StorageConfiguration.swift`
- 修改文件：
  - `Services/QuickHistoryManager.swift`
- 验收要点：
  - 内存缓存：最多保留 10 条（轻量）
  - 磁盘持久化：完整菜谱数据，按需懒加载
  - 后台保存：非阻塞
  - 渐进式加载：先标题，后细节
  - 图片缓存：含 TTL
- 验证命令：
  - `swift test --filter HistoryStorageTests`
  - 压力下懒加载性能测试
- 依赖：`1.2`

### 任务 3.2 — 计划保留策略（状态：未开始，优先级：中，预估：10 小时）

- 描述：保留最近 4 周计划并自动清理。
- 新增文件：
  - `Services/PlansRetentionManager.swift`
  - `Models/RetentionPolicy.swift`
- 修改文件：
  - `Services/StructuredMealPlanGenerator.swift`
- 验收要点：
  - 可视仅最近 4 个自然周计划
  - 保存第 5 周时：自动移除最早一周
  - 非阻塞提示：“为保留最近 4 周，已移除最早一周”
  - 收藏项在“收藏”标签中持续保留（清理不影响）
  - 清理操作具备原子性
- 验证命令：
  - `swift test --filter RetentionPolicyTests`
  - 清理过程中收藏保留测试
- 依赖：`1.5`，`3.1`

### 任务 3.3 — 性能优化（状态：未开始，优先级：中，预估：12 小时）

- 描述：内存管理与响应时间优化。
- 新增文件：
  - `Utils/PerformanceMonitor.swift`
  - `Utils/MemoryManager.swift`
- 修改文件：
  - `Features/Recipes/RecipeHistoryTabView.swift`
  - `Views/HorizontalQuickResultsView.swift`
- 验收要点：
  - 标签切换 < 200ms
  - 综合使用场景内存 < 50MB（10 条快速 + 4 周计划 + 收藏）
  - 生成器 Toast 响应 < 100ms
  - 横向分页 > 5 条时启用虚拟滚动
  - 日历周懒加载
  - 新三标签功能崩溃率 < 0.1%
- 验证命令：
  - `swift test --filter PerformanceTests`
  - 内存剖析（重负载）
  - 响应时间测试
- 依赖：`2.1`，`2.3`

---

## 阶段 4：打磨与无障碍（预计 2 周）

描述：高级动画、无障碍与最终打磨。

### 任务 4.1 — 高级动画与手势（状态：未开始，优先级：中，预估：14 小时）

- 描述：平滑过渡与手势优化。
- 新增文件：
  - `Utils/AnimationManager.swift`
  - `Utils/GestureCoordinator.swift`
- 修改文件：
  - `Views/HorizontalQuickResultsView.swift`
  - `Views/SwipeableMealPlanCalendarView.swift`
  - `Utils/WeekTransitionAnimator.swift`
- 验收要点：
  - 横向分页吸附与动量自然
  - 周与周间导航过渡自然
  - 日历展开/收起动画流畅
  - 标签切换动画与状态保持
  - 正确处理滑动手势冲突
- 验证命令：
  - UI 测试：动画流畅度
  - 手势冲突测试
  - 动画性能剖析
- 依赖：`2.4`，`2.5`

### 任务 4.2 — 无障碍实现（状态：未开始，优先级：中，预估：12 小时）

- 描述：VoiceOver、动态字体与无障碍能力。
- 新增文件：
  - `Utils/AccessibilityManager.swift`
  - `Extensions/AccessibilityExtensions.swift`
- 修改文件：
  - `Features/Recipes/RecipeHistoryTabView.swift`
  - `Views/MealPlanCalendarView.swift`
  - `Views/HorizontalQuickResultsView.swift`
- 验收要点：
  - 容量指示与日历矩阵具备 VoiceOver 描述
  - 通过转子（Rotor）导航日历矩阵
  - 生成器 Toast 支持键盘导航
  - 所有空状态支持动态字体
  - 所有可交互元素具备清晰的 `accessibilityLabel`
  - 开启“降低动态效果”时退化为淡入淡出
- 验证命令：
  - 使用 Xcode Accessibility Inspector 进行无障碍审计
  - VoiceOver 导航测试
  - 动态字体缩放验证
- 依赖：`3.3`，`4.1`

### 任务 4.3 — 导出与分享能力（状态：未开始，优先级：低，预估：10 小时）

- 描述：分享菜谱与导出餐单。
- 新增文件：
  - `Services/ShareManager.swift`
  - `Views/ShareSheet.swift`
  - `Utils/ExportFormatter.swift`
- 修改文件：
  - `Views/QuickResultCardView.swift`
  - `Features/Recipes/FavoritesView.swift`
- 验收要点：
  - 在快速卡片中分享单个菜谱
  - 收藏导出为格式化列表
  - 餐单导出为带日历格式
  - 支持多种导出格式（文本、PDF、日历）
  - 集成系统分享面板
- 验证命令：
  - `swift test --filter ShareManagerTests`
  - 多格式导出格式校验
- 依赖：`2.2`，`1.5`

### 任务 4.4 — 分析与使用洞察（状态：未开始，优先级：低，预估：8 小时）

- 描述：跟踪使用模式与成功指标。
- 新增文件：
  - `Services/AnalyticsManager.swift`
  - `Models/UsageMetrics.swift`
- 修改文件：
  - `Features/Recipes/RecipeHistoryTabView.swift`
  - `Features/Generator/GeneratorToastView.swift`
- 验收要点：
  - 统计三标签使用分布（快速/计划/收藏）
  - 监测生成器 Toast 中“满意/重试”的选择比例
  - 统计各标签平均停留时间
  - 跟踪收藏标记比例
  - 监控标签切换性能指标
  - 符合隐私合规（不采集 PII）
- 验证命令：
  - `swift test --filter AnalyticsTests`
  - 隐私数据合规审计
- 依赖：`1.1`，`2.1`

---

## 验证清单（用于最终验收）

- 生成器 Toast：满意保存并导航，重试保留输入
- 三标签结构：快速（10 条）| 计划（4 周）| 收藏（不限）
- 餐单生成基于结构化“日期+餐别时段”（非事后分组）
- 渐进式容量指示，10/10 不自动删除
- 日历矩阵仅显示结构化“餐单”结果
- 同日截止自动跳过已过餐别
- 空状态符合 HIG 且 CTA 清晰
- 收藏跨标签与保留策略持续有效
- 开启“降低动态效果”时所有动画可优雅降级
- 全局支持 VoiceOver 与动态字体
- 性能目标：标签切换 < 200ms；Toast 响应 < 100ms
- 全量功能内存占用 < 50MB

## 风险监控

- 风险：三标签复杂度过高 → 缓解：清晰标签、遵循 iOS 习惯、渐进披露 → 监控：标签使用分布
- 风险：生成器 Toast 阻碍流程 → 缓解：<100ms 响应、清晰选项 → 监控：选择比例与反馈
- 风险：日历矩阵在大数据量下性能问题 → 缓解：虚拟滚动、懒加载、内存管理 → 监控：内存与响应时间
- 风险：数据迁移复杂 → 缓解：数据模型版本化与迁移脚本 → 监控：迁移成功率与数据一致性

## 完成度指标（Done 定义）

- 代码质量：新增组件测试覆盖率 > 90%；零关键静态告警；性能指标达标
- 用户体验：三标签使用均衡；Toast 满意率 > 80%；收藏参与率 > 20%
- 技术指标：内存 < 50MB；新功能崩溃率 < 0.1%；日期-时段映射 100% 正确

## 自动化与开发提示

- 构建命令：
  - `swift build`
  - `swift test`
  - `xcodebuild -scheme IngredientScanner build`
  - `xcodebuild test -scheme IngredientScanner`
- 开发注意：
  - 遵循既有 SwiftUI 模式
  - 复用现有模型与服务
  - 与当前导航风格保持一致
  - 多设备尺寸下验证日历矩阵可用性
  - 使用 Instruments 核查内存
- 测试优先级：
  - 生成器 Toast 交互流程
  - 日历矩阵导航与性能
  - 收藏跨标签同步
  - 容量管理边界场景
  - 数据持久化与迁移

---

注：本文件为任务跟踪用中文说明，不替代源 `tasks.json` 的机器可读性定义；请以二者同步为准。
