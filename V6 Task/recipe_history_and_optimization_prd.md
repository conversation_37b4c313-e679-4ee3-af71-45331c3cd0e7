# V6 PRD — Recipe Generation History & Enhanced Display with Smart Save

Owner: Augment Agent
Date: 2025-01-XX
Target: iOS 17+, Swift 5.9+, Xcode 15+
Scope: Recipe generation history, tab-based interface, smart save mechanisms, user control features, structured meal plan generation

## 1) Executive Summary
Note: This document uses three tabs throughout (Quick | Plans | Favorites). Previous references to two tabs are updated for consistency.


V6 introduces a comprehensive recipe history system that transforms the current single-view recipe display into a three-tab experience. Users can now access their recent Quick Results and navigate through historical Meal Plans with intuitive swipe gestures. The system includes smart save mechanisms, user control features, performance optimizations, and **critically fixes the meal plan generation architecture** to ensure proper date-based slot allocation.

## 2) Core Objectives

- **Simple User Experience**: No complex time logic that restricts users
- **Generation History**: Save on Good (toast) and display up to 10 quick generations
- **Clear Time Display**: Show when recipes were generated (generation timestamp)
- **User Freedom**: Allow users to generate any meal type at any time without system interference
- **Enhanced Meal Plan Display**: Replace list view with intuitive swipeable 7-day calendar matrix
- **Historical Navigation**: Easy access to past and future meal plans through natural swipe gestures
- **Clean Interface Design**: Separate Quick Results and Meal Plans into distinct, focused experiences
- **Smart Save System**: Intelligent save mechanisms with user control and optimization
- **🎯 Structured Generation**: Fix meal plan generation to use proper date-based slot allocation instead of post-generation grouping

## 3) Interface Architecture - Tab-Based Design

### 3.1 Top-Level Structure
- Segmented control within Recipes page (top placement)
- Three tabs:
  - Quick: Quick results history (save on Good; up to 10 items)
  - Plans: Meal Plan calendar with swipe navigation
  - Favorites: Unified list of favorited recipes from Quick and Plans (not subject to capacity/retention)

### 3.4 Minimal Info Display
- Recipes tab always shows the same minimal info as today (title, time, tags/compatibility chips if available).
- Full details load only after tapping a recipe (unchanged behavior).


### 3.5 Smart Contextual Jump (Quick only)
- In Generator > Quick:
  - User taps Generate → show a toast preview inline in Generator with two buttons: "Good" and "Regenerate"
  - Tap "Good" → save the result and auto-navigate to Recipes > Quick showing the saved card
  - Tap "Regenerate" → dismiss toast; keep the current inputs; user can adjust or tap Generate again for a different result
- Not applied to Meal Plan generator (Meal Plans still auto-navigate on completion per main flow)


### 3.2 Visual Implementation
```
┌─────────────────────────┐
│      📖 Recipes         │ <- Navigation Title
├─────────────────────────┤
│ Quick  │ Plans  │ Favorites │ <- Top Segmented Control
├─────────────────────────┤
│                         │
│    Tab Content Area     │
│                         │
│                         │
└─────────────────────────┘
```

### 3.3 Design Rationale
- Follows iOS design patterns (Apple Music, App Store, Photos)

### 4.0 Quick Tab Header — Progressive Capacity Indicators
```
┌─────────────────────────────────────┐
│ Quick History                [8/10] │
├─────────────────────────────────────┤
│ ●●●●●●●●○○                         │
└─────────────────────────────────────┘
```
- Color feedback: <8/10 neutral; at 8/10 yellow; at 10/10 red with a Manage button
- Manage opens the capacity management picker to delete items

- Clear visual hierarchy: page-level sub-categories, not global navigation
- Maximizes content area by using top placement
- Avoids conflict with main app TabBar
- Thumb-friendly positioning for single-hand operation

## 4) Quick Tab - Quick Results History

### 4.1 Display Features - Horizontal Swipeable Cards
- **Horizontal paging layout** for up to 10 most recent quick generations
- **Swipe navigation**: Left/right swipe to switch between different generation results
- **Page indicator**: Bottom pagination dots or counter (e.g., 1/10)
- **Full-screen card per generation** with vertical scrolling inside each page

### 4.2 Card Content Structure
Each generation page displays:
- **Header**:
  - Generation date and time (e.g., "Sep 1, 14:00")
  - Selected meal type and dish count (e.g., "Lunch · 2 dishes")
- **Recipe list** (vertical scroll):
  - For each recipe: title, cooking time, and ingredient list (truncate long lists)
  - Optional tags/compatibility chips if available (Breakfast/Lunch/Dinner)
- **Actions**:
  - Favorite | Delete

- Visual recipe compatibility: use time-based chips where available — 🌅 Breakfast, ☀️ Lunch, 🌙 Dinner

### 4.3 User Interactions
- **Left/right swipe**: Navigate between generation results (page snapping)
- **Tap a recipe**: Open full recipe detail (existing detail view)
- **Detail view**: Heart button to add/remove favorite
- **Card actions**: Favorite | Delete
- No long-press interactions

### 4.4 Smart Save Features & Storage Logic
- **Storage capacity**: Keep up to 10 most recent quick generations
- Save on Good: Quick results are saved only when the user taps "Good" in the Generator toast (no modal prompts)
- **New result positioning**: When saved, insert at page 1; existing results shift right
- **When capacity is reached (10/10)**:
  - Do NOT evict automatically
  - Show a non-blocking notice: "Quick Recipes full (10). Delete one to save new results."
  - Provide an action: "Manage" → opens a quick picker to delete one (or open Favorites management)
- **Actions per result**: Favorite (protect from deletion) and Delete
### 4.5 Empty State (Quick)
- "No Quick results yet" with a "Generate" button (navigates to Generator tab)

### 5.0 Empty State (Plans) — HIG-aligned
- Conditions: Show full-page empty state when no saved plans exist within the most recent 4 weeks.
- Content: Title "Plan your week with ease"; Subtitle "Create a 7-day meal plan and see each recipe mapped to the exact calendar slot."
- CTAs: "Create 7-day Meal Plan" (primary); "Try a 3-day Plan" (secondary); "How it works" (optional).
- Behavior: CTAs open Generator in Meal Plan mode with prefilled defaults; on success, navigate to and highlight the target week.
- Accessibility: Support Dynamic Type; clear accessibilityLabels; with Reduce Motion, replace animations with crossfades.
## 5) Plans Tab - Meal Plan Calendar

### 5.1 Calendar Matrix
- Swipeable 7-day calendar matrix
- Orientation: rows = Days (Mon…Sun), columns = Meals (Breakfast, Lunch, Dinner)
- **Horizontal swipe navigation**:
  - Swipe left: view next week
  - Swipe right: view previous week
  - View up to the most recent 4 weeks

### 5.3.1 Expandable Calendar (Mobile-friendly)
Collapsed view:
```
┌──────── Jan 1-7, 2025 ─────────┐
│ Mon  Tue  Wed  Thu  Fri  Sat  Sun │
│  3    2    4    1    2    3    2  │
└─────────────────────────────────┘
```
Expanded view (tap week):
```
┌─────────── Jan 1-7, 2025 ───┐
│        Mon   Tue   Wed   Thu   Fri  │
│ Breakfast ●    ○     ●     ●     ○  │
│ Lunch     ●    ●     ●     ○     ●  │
│ Dinner    ●    ●     ●     ○     ●  │
└──────────────────────────────┘
```

### 5.3.2 Navigation and Retention Policies
- Browse window: Back up to 3 previous full weeks; forward limited to [today, today+7 days]; weeks/cells beyond are not navigable.
- Retention: Keep last 4 weeks. When saving a 5th week, auto-remove the oldest week (favorited items persist in Favorites).
- Manage button: allows multi-select of meals/days; actions: Delete or Regenerate


### 5.2 Week Navigation
- **Week indicator**: Show current week date range at top (e.g., "Jan 1-7, 2025")
- **Smooth transitions**: Animated week-to-week navigation with natural swipe gestures
- **Browse window**: Back up to 3 previous full weeks; forward limited to [today, today+7 days]. Cells beyond limits are disabled; weeks beyond are not navigable.

### 5.3 Calendar Layout
```
┌──────────────────────────────────────────────────────┐
│                     Jan 1-7, 2025                    │
├──────────────────────────────────────────────────────┤
│        Mon     Tue     Wed     Thu     Fri     Sat   Sun │
├──────────────────────────────────────────────────────┤
│ Mon        ●       ●       ●       ●       ○       ●    ● │
│ Tue        ○       ●       ●       ●       ●       ●    ● │
│ Wed        ●       ●       ●       ○       ●       ●    ● │
│ Thu        ●       ○       ○       ●       ●       ●    ● │
│ Fri        ○       ●       ●       ●       ●       ●    ● │
│ Sat        ●       ●       ●       ●       ●       ●    ● │
│ Sun        ●       ●       ●       ●       ●       ●    ● │
└──────────────────────────────────────────────────────┘
```
- Row = Day of week (Mon…Sun)
- Column = Meal (Breakfast, Lunch, Dinner)
- Normative requirement: The Plans calendar MUST NOT display Quick results; only structured Meal Plan results mapped to exact (Date × MealType) slots.
- Manage button: allows multi-select of meals/days; actions: Delete or Regenerate

### 5.4 Visual Indicators
- ● = Has meal plan for this slot (tappable)
- ○ = No meal plan for this slot
- ⭐ = Favorited meal plan
- 🔄 = Recently regenerated

### 5.5 Interactive Features
- **Tap filled slots (●)**: View detailed recipes
- **Manage button**: Multi-select meals/days to Delete or Regenerate
- **Detail view**: Heart button to add/remove favorite (persists in Favorites)
- **Empty slots (○)**: Show "No meal planned" with option to generate
- No long-press interactions

## 6) 🎯 Critical Fix: Structured Meal Plan Generation


### 5.6 Overlap/Conflict Explanation
- Overlap can occur if the user generates a new plan that targets dates/meals already saved.
- Examples:
  - Generate a 3‑day plan starting Jan 2 with Lunch + Dinner; later generate a 2‑day plan starting Jan 3 with Dinner only → Jan 3 Dinner overlaps.
  - Regenerate for “this week” repeatedly.
- Policy (simple): saving a plan writes into the exact date+meal slots.
  - If the target slot already has a saved plan:
    - Replace it unless it’s favorited.
    - If favorited, skip replacement and show a brief summary: “Skipped 2 favorited slots; replaced 4 slots.”
  - All changes are atomic per save operation.

### 6.1 Current Architecture Problem ❌
```swift
// BROKEN: Generate flat array, then force-group by index
generateMealIdeas() -> [RecipeUIModel] // No date/meal association
RecipeGrouper.group(items, days: 2, selectedMeals: [.breakfast, .dinner]) // Wrong!
```

### 6.2 Correct Architecture ✅
```swift
struct MealPlanGenerationRequest {
    let startDate: Date           // Specific start date
    let days: Int                 // Number of days (1-7)
    let selectedMeals: [MealType] // Selected meal types
    let mealConfigurations: [MealType: MealConfig]

    // Generate specific slot requirements
    var mealSlots: [MealSlotRequest] {
        var slots: [MealSlotRequest] = []
        let calendar = Calendar.current

        for dayOffset in 0..<days {
            let date = calendar.date(byAdding: .day, value: dayOffset, to: startDate)!

            for mealType in selectedMeals {
                let config = mealConfigurations[mealType]!
                slots.append(MealSlotRequest(
                    date: date,
                    dayIndex: dayOffset,
                    mealType: mealType,
                    dishCount: config.numberOfDishes,
                    maxCookingTime: config.cookingTimeMinutes
                ))
            }
        }
        return slots
    }
}

struct MealSlotRequest {
    let date: Date        // Exact date: 2025-01-15
    let dayIndex: Int     // Relative index: 0, 1, 2...
    let mealType: MealType // breakfast, lunch, dinner
    let dishCount: Int    // Required dishes for this slot
    let maxCookingTime: Int
}
```

### 6.3 Date Range Constraints
```swift
struct MealPlanDateRange {
    static let minStartDate: TimeInterval = 0        // Today
    static let maxStartDate: TimeInterval = 7 * 24 * 60 * 60  // Max 7 days future
    static let maxPlanDuration: Int = 7              // Max 7 days duration

    static func validDateRange(from today: Date = Date()) -> ClosedRange<Date> {
        let calendar = Calendar.current
        let minDate = today
        let maxDate = calendar.date(byAdding: .day, value: 7, to: today)!
        return minDate...maxDate
    }
}
```

### 6.7 Same-Day Meal Cutoffs (Skip Passed Meals)
- When start date = today, meals that have passed their cutoff time are skipped even if selected.
- Default cutoffs (configurable): Breakfast 10:30; Lunch 14:30; Dinner 21:30.
- With Reduce Motion enabled, any transitions degrade to short crossfades.
- User-facing tip: "Skipped meals that have already passed today."

### 6.4 Structured Generation Service
```swift
func generateMealPlan(request: MealPlanGenerationRequest) async throws -> MealPlan {
    var dayPlans: [DayPlan] = []

    // Generate by day
    for dayOffset in 0..<request.days {
        let date = Calendar.current.date(byAdding: .day, value: dayOffset, to: request.startDate)!
        var mealSlots: [MealSlot] = []

        // Generate by meal type
        for mealType in request.selectedMeals {
            let config = request.mealConfigurations[mealType]!

            // Generate recipes for this specific slot
            let recipes = try await generateRecipesForSlot(
                date: date,
                mealType: mealType,
                dishCount: config.numberOfDishes,
                maxTime: config.cookingTimeMinutes
            )

            // Each recipe knows its exact assignment
            let recipesWithMetadata = recipes.map { recipe in
                var r = recipe
                r.assignedDate = date
                r.assignedMealType = mealType
                r.dayIndex = dayOffset
                return r
            }

            mealSlots.append(MealSlot(
                slotId: UUID(),
                date: date,
                dayIndex: dayOffset,
                mealType: mealType,
                recipes: recipesWithMetadata
            ))
        }

        dayPlans.append(DayPlan(date: date, dayIndex: dayOffset, meals: mealSlots))
    }

    return MealPlan(startDate: request.startDate, days: dayPlans)
}
```

### 6.5 Data Models
```swift
struct MealPlan {
    let startDate: Date
    let days: [DayPlan]
}

struct DayPlan {
    let date: Date        // 2025-01-15
    let dayIndex: Int     // 0
    let meals: [MealSlot] // All meals for this day
}

struct MealSlot {
    let slotId: UUID
    let date: Date        // 2025-01-15
    let dayIndex: Int     // 0
    let mealType: MealType // breakfast
    let recipes: [RecipeUIModel] // Exact recipes for this slot
}
```

### 6.6 Default Configuration
```swift
struct DefaultMealPlanConfig {
    static func createDefault() -> MealPlanGenerationRequest {
        let today = Date()
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!

        return MealPlanGenerationRequest(
            startDate: tomorrow,  // Default: start tomorrow
            days: 3,             // Default: 3 days
            selectedMeals: [.lunch, .dinner], // Default: lunch + dinner
            mealConfigurations: [
                .lunch: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2),
                .dinner: MealConfig(cookingTimeMinutes: 45, numberOfDishes: 2)
            ]
        )
    }
}
```

## 7) Smart Save System

### 7.1 Intelligent Save Strategies
```swift
enum SaveStrategy {
    case automatic      // Auto-save all generations
    case confirmPrompt  // Ask user before saving
    case smartDetection // Detect user satisfaction and save accordingly
}
```

### 7.2 Save Flow (Generator Toast — Quick)
1. User generates recipes (Quick)
2. Generator shows a toast preview with "Good" and "Regenerate" options
3. Tap "Good" → save and navigate to Recipes > Quick, insert at position 1 (shift others right)
4. Tap "Regenerate" → dismiss toast; keep inputs; user may adjust or generate again
5. Capacity handling (10 max): If at 10/10, do not auto-evict; show notice with Manage action to delete
6. Actions available on each result: Favorite, Delete

### 7.3 Smart Detection Logic (Legacy)
- **Immediate regeneration**: Likely unsatisfied, ask to replace
- **View recipe details**: Likely interested, save
- **Share recipes**: Definitely satisfied, save and mark favorite
- **Quick exit**: Likely unsatisfied, don't save
- Note: This section is legacy; current flow uses Generator toast (Good/Regenerate) for Quick

### 7.4 Lightweight Storage Model (Updated for 10-Item Quick History)
```swift
struct QuickResultHistory {
    let id: UUID
    let timestamp: Date
    let generationMealType: MealType    // The meal type this generation was for
    let recipeCount: Int
    let recipeTitles: [String]          // Quick preview
    let recipeCompatibilities: [String: [MealType]]  // Recipe -> applicable meals
    let isFavorite: Bool
    let position: Int                   // 1-10, for ordering
    let fullDataKey: String?            // Lazy load full data
    let generationContext: GenerationContext
}

struct GenerationContext {
    let pantrySnapshot: [String]   // Ingredient names only
    let userPreferences: UserPreferences
    let searchQuery: String?
    let mode: GenerationMode
    let dateRange: DateRange?      // For meal plans
    let regenerationTimeWindow: Date?  // For replace detection
}
```

## 8) User Control Features

### 8.1 History Management
- **Delete individual items**: Use Delete action on cards (Quick) or Manage selection (Plans)
- **Clear all history**: Settings → Clear History
- **Export favorites**: Share selected favorite recipes
- **Batch operations**: Use Manage to multi-select and apply actions

### 8.2 Favorites System
- **Star marking**: Tap star icon to favorite/unfavorite
- **Favorites filter**: Show only favorited items
- **Favorites persistence**: Never auto-deleted, user-controlled only

### 8.3 Manage Actions
```swift
enum ManageAction {
    case viewDetails
    case addToFavorites
    case removeFromFavorites
    case regenerate
    case share
    case delete
}
```

## 9) Performance Optimizations

### 9.1 Storage Strategy (Optimized for 10-Item Quick History)
- **Memory cache**: Up to 10 Quick results in memory (lightweight)
- **Disk persistence**: Full recipe data on disk with lazy loading
- **Background saving**: Non-blocking save operations
- **Position management**: Maintain 1-10 ordering, shift positions on new additions
- **Capacity handling**: When at 10/10, do not auto-evict; prompt user to manage (delete one)
- **Favorite protection**: Favorites are protected from eviction when user chooses to delete; in capacity-full state, favorites do not override the need to delete one

### 9.2 Data Loading
- **Progressive loading**: Load titles first, details on demand
- **Image caching**: Cache recipe images with TTL
- **Pagination**: Load older history in chunks

### 9.3 Memory Management (Updated for Quick History)
```swift
class QuickHistoryManager {
    private var quickResults: [QuickResultHistory] = []  // Max 10 items
    private let backgroundQueue = DispatchQueue(label: "history.save")
    private let diskStorage: DiskStorageProtocol

    func saveQuickResult(_ item: QuickResultHistory) async {

### 9.4 Retention Policy for Plans (4 Weeks)
- Keep up to the most recent 4 calendar weeks of saved plans visible.
- When user saves a 5th week, automatically remove the oldest week and show a non-blocking notice: “Removed oldest week to keep the last 4 weeks.”
- Favorites from Plans are added to the Favorites tab and are not lost when older weeks are removed.

        // Handle capacity, positioning, and favorite protection
        await manageCapacityAndSave(item, strategy: strategy)
    }

    private func manageCapacityAndSave(_ item: QuickResultHistory) async {
        // Capacity: do not auto-evict when at 10
        if quickResults.count >= 10 {
            await showCapacityFullNoticeAndOpenManager()
            return
        }
        // Insert at position 1, shift others
        insertAtFront(item)
    }
}
```

## 10) Technical Implementation

### 10.1 Data Models
```swift
struct RecipeGenerationHistory {
    let id: UUID
    let generatedAt: Date
    let mode: UIMode
    let mealType: MealType?
    let recipes: [RecipeUIModel]
    let dishCount: Int
    let mealPlan: MealPlan?
    let isFavorite: Bool
    let saveStrategy: SaveStrategy
    let generationContext: GenerationContext
}

struct WeeklyMealPlan {
    let weekStartDate: Date
    let weekEndDate: Date
    let mealPlan: MealPlan
    let generatedAt: Date


    let isFavorite: Bool
    let metadata: MealPlanMetadata
}

enum RecipeHistoryTab: CaseIterable {
    case quick
    case plans
    case favorites

    var title: String {
        switch self {
        case .quick: return "Quick"
        case .plans: return "Plans"
        case .favorites: return "Favorites"
        }
    }
}
```

### 10.1.1 Favorites Tab
- Third tab in Recipes: Favorites
- Shows a unified, flat list of all favorited recipes (from Quick and Plans)
- Not subject to Quick capacity or Plan week retention
- Items link back to their origin context (quick page or plan slot)

### 10.1.2 Favorites Empty State
- "No favorites yet" with a "Browse Quick" and "Browse Plans" buttons to discover content to favorite

### 10.2 Core Components (Updated)
- `RecipeHistoryTabView`: Main container with segmented control
- `HorizontalQuickResultsView`: **UPDATED** - Horizontal paging view for up to 10 quick results
- `QuickResultPageView`: **NEW** - Individual full-screen page for one generation result
- `RecipeCompatibilityChips`: **NEW** - Meal type chips below recipe tags
- `QuickHistoryManager`: **NEW** - Manages 10-item capacity; no auto-evict; opens manager when full
- `SwipeableMealPlanCalendarView`: 7-day table matrix with swipe navigation
- `WeekIndicatorView`: Shows current week date range
- `MealSlotIndicator`: Visual indicator for meal availability
- `WeekTransitionAnimator`: Handles smooth week-to-week animations
- `SmartSaveManager`: Handles intelligent save strategies with capacity management
- `HistoryStorageService`: Manages data persistence and caching
- `StructuredMealPlanGenerator`: **NEW** - Handles date-based slot generation
- `MealPlanDatePicker`: **NEW** - Date range selection with constraints
- `FavoritesView`: **NEW** - Unified favorites across Quick and Plans

### 10.3 Save Flow Architecture
```swift
protocol SaveStrategyProtocol {
    func shouldSave(for context: GenerationContext) async -> Bool
    func saveWithConfirmation(_ item: HistoryItem) async -> Bool
}

class SmartSaveStrategy: SaveStrategyProtocol {
    func shouldSave(for context: GenerationContext) async -> Bool {
        // Implement smart detection logic
    }
}
```

## 11) User Experience Flows

### 11.1 Tab-Based Navigation
1. User enters Recipes page
2. Sees segmented control with "Quick | Plans | Favorites" at top
3. Default view shows Quick tab (most commonly used)
4. User can switch to Plans tab for meal planning overview or Favorites tab for saved items
5. Each tab maintains its own state and scroll position

### 11.2 Save Flow Summary
- Quick generation uses the Generator toast flow:
  - Good saves and navigates to Recipes > Quick (insert at position 1)
  - Regenerate dismisses toast and preserves inputs
- Meal Plan generation auto-saves into (Date × MealType) slots and auto-navigates to Recipes > Plans to show the generated week
- Capacity handling (Quick): At 10/10, do not auto-evict; show notice with Manage action
- Favorites: Available on cards and in detail; Favorites tab lists all favorited items

### 11.3 Structured Meal Plan Generation Flow
1. User selects "Meal Plan" mode
2. System shows date picker with constraints (today + 7 days max)
3. User selects start date and duration (1-7 days)
4. User selects meal types and configurations
5. System generates recipes for each specific date+meal slot
6. Results display in calendar matrix with exact slot assignments
7. Each slot contains recipes generated specifically for that date+meal

### 11.4 History Management Flow
1. User views history in Quick or Plans tab
2. Use Manage to multi-select items for actions (no long-press)
3. Available actions based on item type and status
4. Batch operations available through edit mode
5. Settings page provides global history management

## 12) Benefits & Value Proposition

### 12.1 User Benefits
- **Predictable**: Users see exactly when they generated what
- **Simple**: No hidden logic or time calculations
- **Flexible**: Generate any meal at any time
- **Focused**: Each tab serves a specific use case without clutter
- **Controlled**: Users have full control over their history
- **Intelligent**: System learns from user behavior
- **Efficient**: Fast loading and smooth interactions
- **🎯 Accurate**: Meal plans have proper date-slot correspondence

### 12.2 Technical Benefits
- **Scalable**: Efficient storage and loading strategies
- **Maintainable**: Clear separation of concerns
- **Extensible**: Easy to add features to each tab independently
- **Performant**: Optimized for memory and disk usage
- **Reliable**: Robust error handling and data integrity
- **🎯 Correct**: Fixes fundamental architecture flaw in meal plan generation

## 13) Implementation Roadmap

### 13.1 Phase 1 (P0) - Core Structure & Critical Fix
- [ ] **🎯 Fix meal plan generation architecture** - Replace post-generation grouping with structured slot-based generation
- [ ] Implement `MealPlanGenerationRequest` with date constraints
- [ ] Create `StructuredMealPlanGenerator` service
- [ ] Add date picker with 7-day constraint
- [ ] Basic three-tab structure with segmented control (Quick | Plans | Favorites)
- [ ] Quick tab with simple card layout
- [ ] Generator toast (Good/Regenerate) save flow for Quick; auto-save for Plans
- [ ] Static meal plan calendar matrix view

### 13.2 Phase 2 (P1) - Enhanced Features
- [ ] Plans empty state (HIG-aligned) with CTAs
- [ ] Same-day meal cutoffs (skip passed meals)
- [ ] Swipeable week navigation with animations
- [ ] Week indicator and date range display
- [ ] Manage-based user control features (no context menus)
- [ ] Proper slot-to-recipe assignment in calendar display

### 13.3 Phase 3 (P2) - Advanced Features
- [ ] Interactive meal slot navigation to recipe details
- [ ] Favorites system with filtering
- [ ] Expandable calendar (collapsed/expanded views)
- [ ] Performance optimizations and caching
- [ ] 1-month storage and automatic cleanup
- [ ] Quick plan options (tomorrow 3 days, this weekend, etc.)

### 13.4 Phase 4 (P3) - Polish & Extensions
- [ ] Advanced animations and gesture refinements
- [ ] Export and sharing capabilities
- [ ] Analytics and usage insights
- [ ] Accessibility enhancements

## 14) Success Metrics

### 14.1 User Engagement
- Three-tab usage distribution (Quick vs Plans vs Favorites)
- Average time spent in each tab
- Favorite marking rate > 20%
- Generator toast Good vs Regenerate selection rates
- Meal plan accuracy: 0% slot misassignment errors

### 14.2 Technical Performance
- Tab switching time < 200ms
- Memory usage < 50MB for 10 Quick + 4 weeks Plans + Favorites
- Generator toast response time < 100ms
- Crash rate < 0.1% in three-tab features
- Generation accuracy: 100% correct date-slot mapping

### 14.3 User Satisfaction
- User retention after three-tab feature introduction
- Support tickets related to tab navigation and favorites
- App store reviews mentioning improved organization
- User feedback on Generator toast flow
- Reduced confusion: Fewer support tickets about "wrong recipes in wrong slots"

## 15) Risk Assessment & Mitigation

### 15.1 Technical Risks
- **Storage growth**: Mitigated by automatic cleanup and lightweight storage
- **Performance degradation**: Mitigated by caching and lazy loading
- **Data corruption**: Mitigated by atomic operations and backup strategies
- **🎯 Migration complexity**: Mitigated by careful data model versioning and migration scripts

### 15.2 UX Risks
- **Three-tab complexity**: Mitigated by familiar iOS patterns and clear tab labels
- **User confusion**: Mitigated by consistent navigation and clear empty states
- **Generator toast friction**: Mitigated by fast response and clear Good/Regenerate options
- **Date constraint frustration**: Mitigated by clear messaging and sensible defaults

### 15.3 Business Risks
- **Development timeline**: Mitigated by phased implementation approach
- **Resource allocation**: Mitigated by clear priority levels and MVP definition
- **User adoption**: Mitigated by gradual rollout and user feedback integration

## 16) Future Considerations

### 16.1 Potential Enhancements
- **iCloud sync**: Cross-device history synchronization
- **Recipe collections**: User-created recipe collections
- **Social sharing**: Share favorite meal plans with friends
- **AI recommendations**: Personalized recipe suggestions based on history
- **🎯 Advanced planning**: Extend date range based on user feedback

### 16.2 Integration Opportunities
- **Calendar app**: Export meal plans to system calendar
- **Health app**: Nutrition tracking integration
- **Grocery apps**: Shopping list generation from meal plans
- **Voice assistants**: Siri shortcuts for common history actions

---

*This PRD represents a comprehensive approach to recipe history management that balances user needs, technical feasibility, and business objectives while maintaining the simplicity and elegance of the existing app experience. **The critical architectural fix ensures meal plans are generated with proper date-slot correspondence, eliminating the fundamental flaw in the current post-generation grouping approach.***
