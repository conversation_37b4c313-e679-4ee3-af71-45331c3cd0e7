{"v6_implementation_tasks": {"metadata": {"plan_version": "V6 - Recipe Generation History & Enhanced Display", "plan_version_code": "v6.0.0", "schema_version": "1.0", "target_platform": "iOS 17+, Swift 5.9+, Xcode 15+", "owner": "AI Implementation Agent", "created_date": "2025-01-09", "estimated_duration": "10-12 weeks", "estimated_days": 70}, "phases": [{"phase_id": 0, "phase_name": "Critical Architecture & Foundation", "description": "Fix meal plan generation architecture and establish three-tab structure", "estimated_weeks": 2, "tasks": [{"task_id": "0.1", "title": "Fix Meal Plan Generation Architecture", "description": "Replace post-generation grouping with structured slot-based generation", "status": "completed", "priority": "critical", "estimated_hours": 16, "files_to_create": ["Services/StructuredMealPlanGenerator.swift", "Models/MealPlanGenerationRequest.swift", "Models/MealSlotRequest.swift", "Models/MealPlan.swift", "Models/DayPlan.swift", "Models/MealSlot.swift"], "files_to_modify": ["Services/RecipeGenerationService.swift", "Services/RecipeRequestBuilder.swift"], "acceptance_criteria": ["MealPlanGenerationRequest generates specific slot requirements", "Each recipe knows its exact date+meal assignment", "Meal plans have proper date-slot correspondence", "Date constraints: today to today+7 days maximum", "Same-day meal cutoffs skip passed meals (Breakfast 10:30, Lunch 14:30, Dinner 21:30)", "Overlap policy: When saving overlapping slots, replace non-favorited; skip favorited; show summary banner; operation is atomic"], "validation_commands": ["swift build", "swift test --filter MealPlanGenerationTests"], "dependencies": []}, {"task_id": "0.2", "title": "Implement Date Range Constraints", "description": "Add MealPlanDateRange with 7-day constraint and same-day cutoffs", "status": "completed", "priority": "critical", "estimated_hours": 8, "files_to_create": ["Utils/MealPlanDateRange.swift", "Utils/MealCutoffManager.swift"], "files_to_modify": [], "acceptance_criteria": ["validDateRange returns today to today+7 days", "Same-day cutoffs skip passed meals automatically", "User-facing tip: 'Skipped meals that have already passed today'", "Configurable cutoff times with sensible defaults"], "validation_commands": ["swift test --filter MealPlanDateRangeTests", "swift test --filter MealCutoffManagerTests"], "dependencies": ["0.1"]}, {"task_id": "0.3", "title": "Create Three-Tab Structure Foundation", "description": "Implement basic RecipeHistoryTabView with segmented control", "status": "completed", "priority": "high", "estimated_hours": 12, "files_to_create": ["Features/Recipes/RecipeHistoryTabView.swift", "Features/Recipes/QuickHistoryView.swift", "Features/Recipes/PlansHistoryView.swift", "Features/Recipes/FavoritesView.swift"], "files_to_modify": ["Features/Recipes/RecipesView.swift"], "acceptance_criteria": ["Three-tab segmented control: Quick | Plans | Favorites", "Each tab maintains its own state and scroll position", "Default view shows Quick tab", "Tab switching time < 200ms", "Proper iOS segmented control styling"], "validation_commands": ["swift build", "UI test tab navigation performance"], "dependencies": []}]}, {"phase_id": 1, "phase_name": "Core Features Implementation", "description": "Generator toast flow, capacity management, and basic calendar", "estimated_weeks": 3, "tasks": [{"task_id": "1.1", "title": "Generator Toast Flow (Quick)", "description": "Implement Smart Contextual Jump with Good/Regenerate options", "status": "completed", "priority": "critical", "estimated_hours": 20, "files_to_create": ["Features/Generator/GeneratorToastView.swift", "Features/Generator/ToastPreviewView.swift", "Models/GeneratorToastState.swift"], "files_to_modify": ["Features/Generator/GeneratorView.swift", "Features/Generator/GeneratorViewModel.swift"], "acceptance_criteria": ["Generate → Toast preview with Good and Regenerate buttons", "Good → Save and navigate to Recipes > Quick (position 1)", "Regenerate → Dismiss toast, keep inputs, allow adjustment", "Toast response time < 100ms", "Only applies to Quick generation (not Meal Plans)", "Meal Plan generation auto-saves and auto-navigates to Recipes > Plans"], "validation_commands": ["swift test --filter GeneratorToastTests", "UI test toast interaction performance"], "dependencies": ["0.3"]}, {"task_id": "1.2", "title": "Quick Results Storage & Capacity Management", "description": "Implement 10-item capacity with progressive indicators", "status": "completed", "priority": "high", "estimated_hours": 16, "files_to_create": ["Services/QuickHistoryManager.swift", "Models/QuickResultHistory.swift", "Views/CapacityIndicatorView.swift"], "files_to_modify": ["Features/Recipes/QuickHistoryView.swift"], "acceptance_criteria": ["Storage capacity: 10 most recent quick generations", "Progressive capacity indicators: ●●●●●●●●○○ with [8/10]", "Color feedback: <8/10 neutral, 8/10 yellow, 10/10 red with Manage button", "No auto-eviction when full - show notice with Manage action", "If capacity = 10, <PERSON> does not save; show notice + Manage action", "New results insert at position 1, shift others right"], "validation_commands": ["swift test --filter QuickHistoryManagerTests", "Test capacity management edge cases"], "dependencies": ["1.1"]}, {"task_id": "1.3", "title": "Basic Calendar Matrix (Plans)", "description": "Static 7-day calendar matrix with meal slots", "status": "completed", "priority": "high", "estimated_hours": 18, "files_to_create": ["Features/Recipes/MealPlanCalendarView.swift", "Views/CalendarMatrixView.swift", "Views/MealSlotIndicator.swift", "Models/WeeklyMealPlan.swift"], "files_to_modify": ["Features/Recipes/PlansHistoryView.swift"], "acceptance_criteria": ["7-day calendar matrix: rows = Days (Mon-Sun), columns = Meals (Breakfast, Lunch, Dinner)", "Visual indicators: ● = Has plan, ○ = No plan, ⭐ = Favorited, 🔄 = Recently regenerated", "Tap filled slots (●) → View detailed recipes", "Normative requirement: Only structured Meal Plan results (no Quick results)", "Basic week indicator showing date range"], "validation_commands": ["swift test --filter MealPlanCalendarTests", "UI test calendar slot interactions"], "dependencies": ["0.1", "0.3"]}, {"task_id": "1.4", "title": "Empty States Implementation", "description": "HIG-aligned empty states for all three tabs", "status": "completed", "priority": "medium", "estimated_hours": 10, "files_to_create": ["Views/QuickEmptyStateView.swift", "Views/PlansEmptyStateView.swift", "Views/FavoritesEmptyStateView.swift"], "files_to_modify": ["Features/Recipes/QuickHistoryView.swift", "Features/Recipes/PlansHistoryView.swift", "Features/Recipes/FavoritesView.swift"], "acceptance_criteria": ["Quick: 'No Quick results yet' with Generate button (navigates to Generator)", "Plans: 'Plan your week with ease' with <PERSON><PERSON> 7-day/3-day Plan CTAs", "Favorites: 'No favorites yet' with <PERSON><PERSON><PERSON> Quick/Plans discovery buttons", "All empty states support Dynamic Type and accessibility", "CTAs open Generator in appropriate mode with prefilled defaults"], "validation_commands": ["swift test --filter EmptyStateTests", "Accessibility audit for empty states"], "dependencies": ["1.1", "1.3"]}, {"task_id": "1.5", "title": "Basic Favorites System", "description": "Heart button favorites with cross-tab persistence", "status": "completed", "priority": "medium", "estimated_hours": 14, "files_to_create": ["Services/FavoritesManager.swift", "Models/FavoriteItem.swift", "Views/FavoriteButton.swift"], "files_to_modify": ["Features/Recipes/RecipeDetailView.swift", "Features/Recipes/FavoritesView.swift"], "acceptance_criteria": ["Heart button in recipe detail views to add/remove favorites", "Favorites persist across Quick and Plans tabs", "Favorites tab shows unified flat list from both sources", "Items link back to origin context (quick page or plan slot)", "Favorites not subject to Quick capacity or Plan week retention", "Favorite protection from deletion in Quick tab", "Favorites empty state buttons (Browse Quick/Plans) navigate to the corresponding tabs"], "validation_commands": ["swift test --filter FavoritesManagerTests", "Test cross-tab favorite persistence"], "dependencies": ["1.2", "1.3"]}]}, {"phase_id": 2, "phase_name": "Enhanced UI Features", "description": "Horizontal paging, swipeable navigation, and expandable calendar", "estimated_weeks": 3, "tasks": [{"task_id": "2.1", "title": "Horizontal Paging for Quick History", "description": "Implement swipeable cards with page indicators", "status": "completed", "priority": "high", "estimated_hours": 20, "files_to_create": ["Views/HorizontalQuickResultsView.swift", "Views/QuickResultPageView.swift", "Views/PageIndicatorView.swift"], "files_to_modify": ["Features/Recipes/QuickHistoryView.swift"], "acceptance_criteria": ["Horizontal paging layout for up to 10 quick generations", "Left/right swipe navigation with page snapping", "Page indicator: dots (●○○○○) or counter (1/10)", "Full-screen card per generation with vertical scrolling inside", "Smooth transitions with proper momentum", "Memory-efficient virtual scrolling for performance"], "validation_commands": ["swift test --filter HorizontalQuickResultsTests", "UI test swipe performance and memory usage"], "dependencies": ["1.2"]}, {"task_id": "2.2", "title": "Quick Result Card Content", "description": "Recipe cards with time-based compatibility chips", "status": "completed", "priority": "high", "estimated_hours": 16, "files_to_create": ["Views/RecipeCompatibilityChips.swift", "Views/QuickResultCardView.swift", "Views/RecipeCardHeaderView.swift"], "files_to_modify": ["Views/QuickResultPageView.swift"], "acceptance_criteria": ["Header: Generation date/time, meal type, dish count", "Recipe list: title, cooking time, ingredient list (truncated)", "Time-based compatibility chips: 🌅 Breakfast, ☀️ Lunch, 🌙 Dinner", "Actions: Favorite | Delete buttons", "Tap recipe → Open full recipe detail view", "Clean, readable layout following minimal info display principle"], "validation_commands": ["swift test --filter QuickResultCardTests", "Visual regression testing for card layout"], "dependencies": ["2.1"]}, {"task_id": "2.3", "title": "Swipeable Week Navigation", "description": "Animated week-to-week calendar navigation", "status": "completed", "priority": "high", "estimated_hours": 18, "files_to_create": ["Views/SwipeableMealPlanCalendarView.swift", "Views/WeekIndicatorView.swift", "Utils/WeekTransitionAnimator.swift"], "files_to_modify": ["Features/Recipes/MealPlanCalendarView.swift"], "acceptance_criteria": ["Horizontal swipe: left = next week, right = previous week", "Browse window: Back 3 weeks, forward to [today, today+7 days]", "Week indicator shows date range (e.g., 'Jan 1-7, 2025')", "Smooth animated transitions between weeks", "Weeks/cells beyond limits are not navigable", "With Reduce Motion: transitions degrade to crossfades"], "validation_commands": ["swift test --filter SwipeableCalendarTests", "Accessibility test for Reduce Motion"], "dependencies": ["1.3"]}, {"task_id": "2.4", "title": "Expandable Calendar Views", "description": "Mobile-friendly collapsed/expanded calendar states", "status": "completed", "priority": "medium", "estimated_hours": 14, "files_to_create": ["Views/CollapsedCalendarView.swift", "Views/ExpandedCalendarView.swift", "Utils/CalendarExpansionAnimator.swift"], "files_to_modify": ["Views/SwipeableMealPlanCalendarView.swift"], "acceptance_criteria": ["Collapsed view: meal counts per day (3 2 4 1 2 3 2)", "Expanded view: full matrix when user taps week", "Smooth tap-to-expand/collapse animations", "Manage button in expanded view for multi-select operations", "Proper state management for collapsed/expanded per week"], "validation_commands": ["swift test --filter ExpandableCalendarTests", "UI test expansion animation performance"], "dependencies": ["2.3"]}, {"task_id": "2.5", "title": "Manage & Batch Operations", "description": "Multi-select operations for calendar and quick results", "status": "completed", "priority": "medium", "estimated_hours": 12, "files_to_create": ["Views/ManageSelectionView.swift", "Utils/BatchOperationManager.swift", "Models/ManageAction.swift"], "files_to_modify": ["Features/Recipes/QuickHistoryView.swift", "Views/SwipeableMealPlanCalendarView.swift"], "acceptance_criteria": ["Manage button enables multi-select mode", "Quick: Multi-select cards for Delete operations", "Plans: Multi-select meals/days for Delete or Regenerate", "Actions: viewDetails, addToFavorites, removeFromFavorites, regenerate, share, delete", "Clear selection state management", "Batch operations are atomic (all succeed or all fail)", "Respect favorites semantics: prevent deleting favorited items in Quick; handle Plan regen/delete by skipping favorited slots and reporting summary"], "validation_commands": ["swift test --filter BatchOperationTests", "Test atomic batch operations"], "dependencies": ["2.2", "2.4"]}]}, {"phase_id": 3, "phase_name": "Data Management & Storage", "description": "Storage policies, retention management, and performance optimization", "estimated_weeks": 2, "tasks": [{"task_id": "3.1", "title": "Storage Strategy Implementation", "description": "Memory cache with disk persistence and lazy loading", "status": "not_started", "priority": "high", "estimated_hours": 16, "files_to_create": ["Services/HistoryStorageService.swift", "Utils/DiskStorageManager.swift", "Models/StorageConfiguration.swift"], "files_to_modify": ["Services/QuickHistoryManager.swift"], "acceptance_criteria": ["Memory cache: Up to 10 Quick results in memory (lightweight)", "Disk persistence: Full recipe data with lazy loading", "Background saving: Non-blocking save operations", "Progressive loading: Load titles first, details on demand", "Image caching with TTL for recipe images"], "validation_commands": ["swift test --filter HistoryStorageTests", "Performance test lazy loading under load"], "dependencies": ["1.2"]}, {"task_id": "3.2", "title": "Plans Retention Policy", "description": "4-week retention with automatic cleanup", "status": "not_started", "priority": "medium", "estimated_hours": 10, "files_to_create": ["Services/PlansRetentionManager.swift", "Models/RetentionPolicy.swift"], "files_to_modify": ["Services/StructuredMealPlanGenerator.swift"], "acceptance_criteria": ["Keep last 4 calendar weeks of saved plans visible", "When saving 5th week: auto-remove oldest week", "Show non-blocking notice: 'Removed oldest week to keep the last 4 weeks'", "Favorited items persist in Favorites tab (not lost during cleanup)", "Atomic cleanup operations"], "validation_commands": ["swift test --filter RetentionPolicyTests", "Test favorite preservation during cleanup"], "dependencies": ["1.5", "3.1"]}, {"task_id": "3.3", "title": "Performance Optimization", "description": "Memory management and response time optimization", "status": "not_started", "priority": "medium", "estimated_hours": 12, "files_to_create": ["Utils/PerformanceMonitor.swift", "Utils/MemoryManager.swift"], "files_to_modify": ["Features/Recipes/RecipeHistoryTabView.swift", "Views/HorizontalQuickResultsView.swift"], "acceptance_criteria": ["Tab switching time < 200ms", "Memory usage < 50MB for 10 Quick + 4 weeks Plans + Favorites", "Generator toast response time < 100ms", "Virtual scrolling for horizontal paging beyond 5 items", "Lazy loading of calendar weeks", "Crash rate < 0.1% in three-tab features"], "validation_commands": ["swift test --filter PerformanceTests", "Memory profiling under heavy load", "Response time testing"], "dependencies": ["2.1", "2.3"]}]}, {"phase_id": 4, "phase_name": "Polish & Accessibility", "description": "Advanced animations, accessibility, and final polish", "estimated_weeks": 2, "tasks": [{"task_id": "4.1", "title": "Advanced Animations & Gestures", "description": "Smooth transitions and gesture refinements", "status": "not_started", "priority": "medium", "estimated_hours": 14, "files_to_create": ["Utils/AnimationManager.swift", "Utils/GestureCoordinator.swift"], "files_to_modify": ["Views/HorizontalQuickResultsView.swift", "Views/SwipeableMealPlanCalendarView.swift", "Utils/WeekTransitionAnimator.swift"], "acceptance_criteria": ["Smooth page snapping with proper momentum in horizontal paging", "Natural week-to-week navigation with animated transitions", "Calendar expansion/collapse with fluid animations", "Tab switching animations and state preservation", "Proper gesture conflict resolution between swipes"], "validation_commands": ["UI test animation smoothness", "Gesture conflict testing", "Animation performance profiling"], "dependencies": ["2.4", "2.5"]}, {"task_id": "4.2", "title": "Accessibility Implementation", "description": "VoiceOver, Dynamic Type, and accessibility features", "status": "not_started", "priority": "medium", "estimated_hours": 12, "files_to_create": ["Utils/AccessibilityManager.swift", "Extensions/AccessibilityExtensions.swift"], "files_to_modify": ["Features/Recipes/RecipeHistoryTabView.swift", "Views/MealPlanCalendarView.swift", "Views/HorizontalQuickResultsView.swift"], "acceptance_criteria": ["VoiceOver support for capacity indicators and calendar matrix", "Calendar matrix navigation with rotor control", "Generator toast keyboard navigation support", "Dynamic Type support in all empty states", "Clear accessibilityLabels for all interactive elements", "Reduce Motion: animations degrade to crossfades"], "validation_commands": ["Accessibility audit with Xcode Accessibility Inspector", "VoiceOver navigation testing", "Dynamic Type scaling verification"], "dependencies": ["3.3", "4.1"]}, {"task_id": "4.3", "title": "Export & Sharing Capabilities", "description": "Share recipes and export meal plans", "status": "not_started", "priority": "low", "estimated_hours": 10, "files_to_create": ["Services/ShareManager.swift", "Views/ShareSheet.swift", "Utils/ExportFormatter.swift"], "files_to_modify": ["Views/QuickResultCardView.swift", "Features/Recipes/FavoritesView.swift"], "acceptance_criteria": ["Share individual recipes from Quick cards", "Export favorites as formatted lists", "Share meal plans with calendar formatting", "Support multiple export formats (text, PDF, calendar)", "Integration with system share sheet"], "validation_commands": ["swift test --filter ShareManagerTests", "Test export formatting across different formats"], "dependencies": ["2.2", "1.5"]}, {"task_id": "4.4", "title": "Analytics & Usage Insights", "description": "Track usage patterns and success metrics", "status": "not_started", "priority": "low", "estimated_hours": 8, "files_to_create": ["Services/AnalyticsManager.swift", "Models/UsageMetrics.swift"], "files_to_modify": ["Features/Recipes/RecipeHistoryTabView.swift", "Features/Generator/GeneratorToastView.swift"], "acceptance_criteria": ["Track three-tab usage distribution (Quick vs Plans vs Favorites)", "Monitor Generator toast Good vs Regenerate selection rates", "Measure average time spent in each tab", "Track favorite marking rate", "Monitor tab switching performance metrics", "Privacy-compliant analytics without PII"], "validation_commands": ["swift test --filter AnalyticsTests", "Privacy audit for analytics data"], "dependencies": ["1.1", "2.1"]}]}], "validation_checklist": ["Generator toast flow: Good saves & navigates, Regenerate preserves inputs", "Three-tab structure: Quick (10 items) | Plans (4 weeks) | Favorites (unlimited)", "Meal plan generation uses structured date+meal slots (not post-grouping)", "Progressive capacity indicators with no auto-eviction at 10/10", "Calendar matrix shows only structured Meal Plan results", "Same-day meal cutoffs skip passed meals automatically", "Empty states are HIG-aligned with clear CTAs", "Favorites persist across tabs and retention policies", "All animations degrade gracefully with Reduce Motion", "VoiceOver and Dynamic Type support throughout", "Performance targets: <200ms tab switching, <100ms toast response", "Memory usage < 50MB for full feature set"], "risk_monitoring": [{"risk": "Three-tab complexity overwhelming users", "mitigation": "Clear tab labels, familiar iOS patterns, progressive disclosure", "monitoring": "Tab usage distribution analytics"}, {"risk": "Generator toast friction slowing workflow", "mitigation": "Fast response times (<100ms), clear Good/Regenerate options", "monitoring": "Toast selection rates and user feedback"}, {"risk": "Calendar matrix performance with large datasets", "mitigation": "Virtual scrolling, lazy loading, memory management", "monitoring": "Memory usage and response time metrics"}, {"risk": "Data migration complexity from current structure", "mitigation": "Careful data model versioning and migration scripts", "monitoring": "Migration success rates and data integrity checks"}], "completion_metrics": {"code_quality": {"test_coverage": "> 90% for new components", "static_analysis": "Zero critical warnings", "performance": "All response time targets met"}, "user_experience": {"three_tab_usage": "Balanced usage across all tabs", "generator_toast_satisfaction": "> 80% Good selections", "favorite_engagement": "> 20% favorite marking rate"}, "technical": {"memory_efficiency": "< 50MB total usage", "crash_rate": "< 0.1% in new features", "generation_accuracy": "100% correct date-slot mapping"}}, "automation_hints": {"build_commands": ["swift build", "swift test", "xcodebuild -scheme IngredientScanner build", "xcodebuild test -scheme IngredientScanner"], "development_notes": ["Follow existing SwiftUI patterns in codebase", "Use existing IngredientScanner models and services where possible", "Maintain consistency with current app navigation patterns", "Test on multiple device sizes for calendar matrix usability", "Verify memory usage with Instruments during development"], "testing_priorities": ["Generator toast interaction flows", "Calendar matrix navigation and performance", "Cross-tab favorite synchronization", "Capacity management edge cases", "Data persistence and migration"]}}}