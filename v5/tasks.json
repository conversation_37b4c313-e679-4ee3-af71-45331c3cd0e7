{"v5_implementation_tasks": {"metadata": {"plan_version": "V5 - Pantry Name Normalization & Per-Meal Generation", "plan_version_code": "v5.0.0", "schema_version": "1.0", "target_platform": "iOS 17+, Swift 5.9+, Xcode 15+", "owner": "Augment Agent", "created_date": "2025-09-04", "estimated_duration": "2-3 weeks", "estimated_days": 14}, "phases": [{"phase_id": 1, "phase_name": "Foundation & Cleanup", "description": "Core utilities and project cleanup", "tasks": [{"task_id": "1.1", "title": "Implement NameCanonicalizer", "description": "Create Utils/NameCanonicalizer.swift with Unicode NFC normalization, title-case rules, and compound word handling", "status": "done", "files_to_create": ["Utils/NameCanonicalizer.swift"], "files_to_modify": [], "acceptance_criteria": ["Handles Unicode accented characters (crème brûlée, jalapeño)", "Title-cases with English stopwords (and, or, of, the, a, an, in)", "Preserves acronyms (BBQ, A2 Milk)", "Handles compound words with hyphen exceptions", ">= 90% unit test coverage for NameCanonicalizer edge cases"], "estimated_hours": 8, "priority": "critical"}, {"task_id": "1.2", "title": "Project Cleanup - Build Artifacts", "description": "Remove build artifacts and archive V4 documentation", "status": "done", "files_to_create": ["archive/v4-tasks/", "archive/reports/"], "files_to_modify": [".giti<PERSON>re"], "files_to_remove": [".DS_Store", "build.log", "build.out", "build2.out", "test.out", "IngredientScanner.xcodeproj/project.pbxproj.backup"], "globs_to_remove": ["**/*.backup"], "acceptance_criteria": ["All build artifacts removed", "V4 Tasks archived to archive/v4-tasks/", "Project size reduced by ~3MB", ".gitignore updated with new patterns"], "estimated_hours": 2, "priority": "high", "validation_commands": ["find . -name \"*.log\" -o -name \"*.out\" -o -name \"*.backup\" -print", "find \"V4 Tasks\" -maxdepth 1 -print 2>/dev/null || echo 'V4 Tasks not found'"]}]}, {"phase_id": 2, "phase_name": "Core Pantry Logic", "description": "Atomic pantry operations with normalization", "tasks": [{"task_id": "2.1", "title": "SwiftDataStorageService Transaction Wrapper", "description": "Add performTransaction(_:) wrapper for atomic operations", "status": "done", "files_to_create": [], "files_to_modify": ["Services/SwiftDataStorageService.swift"], "acceptance_criteria": ["Atomic transaction wrapper implemented", "Rollback on failure functionality", "Thread-safe operation", "Error handling with proper Swift error types"], "estimated_hours": 4, "priority": "critical"}, {"task_id": "2.2", "title": "PantryService Replace-on-Add Logic", "description": "Implement addOrReplaceIngredients with canonicalization and batch deduplication", "status": "done", "files_to_create": [], "files_to_modify": ["Services/PantryService.swift"], "acceptance_criteria": ["Same category + canonical name triggers replace (not insert)", "Batch deduplication (first wins for same canonical key)", "Atomic upsert with rollback on failure", "Date reset (purchaseDate, dateAdded, notificationCycle = 0)", "markAsRecentlyAdded works with updated items", "Simulate failure mid-batch; assert full rollback and no partial updates"], "simulation_scenarios": [{"scenario": "Mid-batch failure", "expect": "No partial updates; pantry unchanged"}, {"scenario": "Network failure during persist", "expect": "Full rollback; no items modified"}, {"scenario": "Duplicate canonical names in batch", "expect": "First wins; subsequent duplicates ignored"}], "estimated_hours": 12, "priority": "critical", "dependencies": ["1.1", "2.1"]}, {"task_id": "2.3", "title": "Wire Replace-on-Add to All Entry Points", "description": "Update all ingredient addition flows to use new addOrReplaceIngredients", "status": "done", "files_to_create": [], "files_to_modify": ["Features/3_Results/ResultsViewModel.swift", "Features/Pantry/PantryViewModel.swift"], "acceptance_criteria": ["Scan Results → addSelectedToPantry uses replace-on-add", "Pantry Add sheet (library + custom) uses replace-on-add", "Manual add path uses replace-on-add", "Recently added highlighting works correctly"], "estimated_hours": 8, "priority": "critical", "dependencies": ["2.2"]}]}, {"phase_id": 3, "phase_name": "Library & Legacy Cleanup", "description": "IngredientLibrary normalization and Organizer removal", "tasks": [{"task_id": "3.1", "title": "IngredientLibrary Canonicalization", "description": "Apply canonicalization at load time and remove ingredients.txt dependency", "status": "done", "files_to_create": [], "files_to_modify": ["Services/IngredientLibrary.swift"], "files_to_remove": ["ingredients.txt"], "acceptance_criteria": ["Load-time canonicalization for all library entries", "categoryToItems uses canonical names, sorted case-insensitively", "nameToCategory with canonical and lowercase keys", "ingredients.txt removed from repo and Xcode project", "Suggestions return canonicalized names"], "estimated_hours": 6, "priority": "high", "dependencies": ["1.1"]}, {"task_id": "3.2", "title": "Remove Organizer Feature", "description": "Complete removal of PantryOrganizerService and related components", "status": "done", "files_to_create": [], "files_to_modify": ["Features/Pantry/PantryView.swift", "Features/Pantry/PantryViewModel.swift", "Services/ServiceContainer.swift"], "files_to_verify": ["Features/Pantry/PantryView.swift", "Features/Pantry/PantryViewModel.swift", "Services/ServiceContainer.swift"], "files_to_remove": ["Services/PantryOrganizerService.swift", "Tests/PantryOrganizerServiceTests.swift"], "acceptance_criteria": ["PantryOrganizerService deleted", "Organizer UI button removed from PantryView", "Related models (UpdateOp, MergeOp, CleanUpPlan) deleted", "ServiceContainer updated to remove organizer dependencies", "Build succeeds without organizer references"], "estimated_hours": 4, "priority": "medium"}]}, {"phase_id": 4, "phase_name": "Generation & Caching", "description": "Per-meal generation with JSON mode and stable caching", "tasks": [{"task_id": "4.1", "title": "Stable Cache Key Generator", "description": "Create Utils/CacheKeyGenerator.swift for stable, order-independent cache keys", "status": "done", "files_to_create": ["Utils/CacheKeyGenerator.swift"], "files_to_modify": [], "acceptance_criteria": ["Stable keys regardless of pantry item ordering", "SHA256 hash of canonical sorted inputs", "Separate hashes for pantry, preferences, meal specifications", "Unit tests verify order independence", "Same pantry in different orders yields identical cache keys"], "estimated_hours": 4, "priority": "high"}, {"task_id": "4.2", "title": "Per-Meal Generation with JSON Mode", "description": "Update recipe generation to enforce per-meal counts with JSON MIME type", "status": "done", "files_to_create": [], "files_to_modify": ["Services/RecipeGenerationService.swift", "Services/RecipeRequestBuilder.swift", "Services/RecipeServiceAdapter.swift"], "acceptance_criteria": ["generationConfig.response_mime_type = 'application/json' set", "Per-meal counts propagated from UI to generation", "Strict per-meal schema for selected meals only (unrequested meals absent)", "JSON parsing with text fallback for compatibility", "Single retry if meal array.count < N, then filler if still short"], "estimated_hours": 16, "priority": "critical", "dependencies": ["4.1"]}, {"task_id": "4.3", "title": "<PERSON><PERSON> Cache with TTL", "description": "Implement RecipeDetailCache with 48h TTL disk persistence", "status": "done", "files_to_create": ["Services/RecipeDetailCache.swift"], "files_to_modify": [], "acceptance_criteria": ["Disk-backed cache with 48h TTL", "Cache keyed by stable cache keys", "Detail prefetch after ideas generation", "Cache cleanup for expired entries", "Thread-safe cache operations"], "estimated_hours": 10, "priority": "high", "dependencies": ["4.1"]}]}, {"phase_id": 5, "phase_name": "Documentation & Quality", "description": "Documentation cleanup and technical debt resolution", "tasks": [{"task_id": "5.1", "title": "Documentation Audit", "description": "Archive outdated reports and update README.md for V5", "status": "done", "files_to_create": ["archive/reports/"], "files_to_modify": ["README.md"], "files_to_move": [{"from": "auth_state_implementation_report.md", "to": "archive/reports/"}, {"from": "firebase_config_audit_report.md", "to": "archive/reports/"}, {"from": "iOS17_Upgrade_Optimization_Report.md", "to": "archive/reports/"}, {"from": "AI_Recipe_Generator_Progress_Tracker.md", "to": "archive/reports/"}], "acceptance_criteria": ["Outdated reports archived", "README.md reflects V5 architecture changes", "Root directory cleaned of outdated documentation", "No broken internal links (verified with link checker)"], "validation_commands": ["find . -name \"*.md\" -exec grep -l \"\\[.*\\](.*\\.md)\" {} \\; | head -5", "grep -r \"V4 Tasks\\|auth_state_implementation\" --include=\"*.md\" . || echo 'No references found'"], "estimated_hours": 3, "priority": "medium"}, {"task_id": "5.2", "title": "Technical Debt Cleanup", "description": "Resolve TODO/FIXME items and static analysis warnings", "status": "done", "files_to_create": [], "files_to_modify": ["Coordinator/AppCoordinator.swift", "Features/Profile/PreferencesEditView.swift"], "files_to_verify": ["Coordinator/AppCoordinator.swift", "Features/Profile/PreferencesEditView.swift", "Services/ServiceContainer.swift"], "acceptance_criteria": ["TODO/FIXME count reduced (conservative fixes only)", "Static analysis warnings resolved (non-breaking changes)", "Development-only debug code removed/consolidated", "Build warnings eliminated"], "estimated_hours": 6, "priority": "low", "validation_commands": ["grep -r \"TODO\\|FIXME\" --include=\"*.swift\" . | wc -l"]}]}, {"phase_id": 6, "phase_name": "Testing & Validation", "description": "Comprehensive testing and final validation", "tasks": [{"task_id": "6.1", "title": "Unit Test Implementation", "description": "Comprehensive unit tests for all new components", "status": "not_started", "files_to_create": ["Tests/NameCanonicalizerTests.swift", "Tests/CacheKeyGeneratorTests.swift", "Tests/PantryServiceReplaceTests.swift", "Tests/RecipeDetailCacheTests.swift"], "files_to_modify": [], "acceptance_criteria": ["NameCanonicalizer: Unicode, stopwords, compounds, edge cases", "PantryService: Atomic operations, batch deduplication, rollback", "CacheKeyGenerator: Order independence, hash stability", "RecipeDetailCache: TTL behavior, disk persistence", "Generation: Per-meal counts, JSON/text parsing, retry logic"], "estimated_hours": 20, "priority": "critical"}, {"task_id": "6.2", "title": "Integration Testing", "description": "End-to-end testing of complete user flows", "status": "not_started", "files_to_create": [], "files_to_modify": [], "acceptance_criteria": ["Scan Results → Pantry: Replace-on-add works, highlights correct items", "Add Sheet → Pantry: Library/custom items normalized and replaced", "Recipe Generation: Exact per-meal counts, caching effective", "Cache persistence: Survives app restart within TTL"], "estimated_hours": 12, "priority": "high", "dependencies": ["6.1"]}, {"task_id": "6.3", "title": "Final Build & Regression Testing", "description": "Complete build validation and regression testing", "status": "not_started", "files_to_create": [], "files_to_modify": [], "acceptance_criteria": ["Clean build: iOS 17+ target, Swift 5.9+, Xcode 15+", "All unit tests pass", "All integration tests pass", "Performance regression check", "Memory leak verification"], "estimated_hours": 8, "priority": "critical", "dependencies": ["6.2"], "validation_commands": ["xcodebuild -scheme IngredientScanner build", "xcodebuild test -scheme IngredientScanner", "grep -r \"TODO\\|FIXME\" --include=\"*.swift\" . | wc -l"]}]}], "validation_checklist": ["Adding identical item (same category, canonical name) replaces existing with date reset", "Pantry names consistently canonicalized from all sources", "IngredientLibrary suggestions show canonical names", "Organizer button and functionality completely removed", "Generator returns exactly N recipes per meal with JSON response mode", "Detail view faster due to prefetch + cache", "Build artifacts removed; project size reduced by ~3MB", "Documentation consolidated; root directory cleaner", "TODO/FIXME count reduced; static analysis warnings resolved", "Replace operations atomic (no partial updates on failure)", "Cache keys stable regardless of pantry item ordering", "JSON MIME responses parsed correctly; text fallback works", "Batch operations deduplicate identical items within request"], "risk_monitoring": [{"risk": "Transaction failures leaving partial state", "mitigation": "Atomic operations with rollback", "monitoring": "Error telemetry for transaction failures"}, {"risk": "Cache key instability from input ordering", "mitigation": "Canonical sorting before hashing", "monitoring": "Cache hit rate metrics"}, {"risk": "JSON MIME compatibility issues", "mitigation": "Fallback to text parsing", "monitoring": "Parse failure rate tracking"}], "completion_metrics": {"code_quality": {"todo_fixme_reduction": "> 50% reduction in TODO/FIXME count", "static_analysis": "Zero critical warnings", "test_coverage": "> 90% for new components"}, "performance": {"project_size_reduction": "~3MB reduction", "cache_hit_rate": "> 80% for recipe details", "generation_reliability": "> 95% success rate for per-meal counts"}, "functionality": {"duplicate_prevention": "100% prevention of same-name duplicates within the same category", "normalization_consistency": "100% canonical naming across all sources", "atomic_operations": "100% transaction success or full rollback"}}, "automation_hints": {"shell": "bash", "cwd_assumption": "Repository root", "notes": ["files_to_move expects {from,to} objects and will create destination directories if missing", "globs_to_remove are evaluated using find(1); patterns match recursively", "validation_commands are advisory and may need env-specific adjustments"]}}}