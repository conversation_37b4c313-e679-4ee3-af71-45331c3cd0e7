# V5 Implementation Package

## 📋 Overview
This folder contains the complete implementation package for V5: Pantry Name Normalization, Replace-on-Add, Library Cleanup, and Per-Meal Generation Enforcement.

## 📁 Package Contents

### Core Documents
- **`pantry_normalization_and_generation_plan.md`** - Complete V5 technical specification and requirements
- **`tasks.json`** - Detailed implementation tasks with dependencies, validation, and automation support
- **`README.md`** (this file) - Implementation guide and handover instructions


## 🧭 Start Here (Handoff Quickstart)
- Branch: hao6
- Order of work: 1.1 → 1.2 → 2.1 → 2.2 → 2.3 → 3.1 → 3.2 → 4.1 → 4.2 → 4.3 → 5.1 → 5.2 → 6.1 → 6.2 → 6.3
- Build: `xcodebuild -scheme IngredientScanner build`
- Test: `xcodebuild test -scheme IngredientScanner`
- Update progress: set each task "status" in v5/tasks.json to in_progress/done as you go
- Keep PRs small and focused; run tests before committing

## 🎯 Implementation Approach

### Prerequisites
- **Target Platform**: iOS 17+, Swift 5.9+, Xcode 15+
- **Repository Root**: All paths assume execution from `/Users/<USER>/IngredientScanner VSCode Augment Project/ingredient-scanner/`
- **Shell Environment**: Bash (validation commands optimized for bash)

### Phase Execution Order
Execute tasks in strict phase order due to dependencies:

1. **Phase 1: Foundation & Cleanup** (Tasks 1.1, 1.2)
2. **Phase 2: Core Pantry Logic** (Tasks 2.1, 2.2, 2.3)
3. **Phase 3: Library & Legacy Cleanup** (Tasks 3.1, 3.2)
4. **Phase 4: Generation & Caching** (Tasks 4.1, 4.2, 4.3)
5. **Phase 5: Documentation & Quality** (Tasks 5.1, 5.2)
6. **Phase 6: Testing & Validation** (Tasks 6.1, 6.2, 6.3)

### Task Status Tracking
All tasks include `status` field for progress tracking:
- `not_started` → `in_progress` → `done`

## 🔧 Automation Support

### File Operations
- **files_to_create**: Create new files/directories
- **files_to_modify**: Update existing files
- **files_to_remove**: Delete specific files
- **files_to_move**: Structured objects with `{from, to}` format
- **globs_to_remove**: Glob patterns for batch deletion
- **files_to_verify**: Check existence before modification

### Validation Commands
- Designed for bash shell execution
- Include error handling (`2>/dev/null || echo`)
- Use `find` instead of shell globs for reliability

### Key Automation Hints
- Repository root assumed as working directory
- Destination directories created automatically for file moves
- Validation commands are advisory and may need environment-specific adjustments

## 🚀 Quick Start

### Step 1: Validate Environment
```bash
# Check target files exist
find . -name "Services/PantryService.swift" -o -name "Services/IngredientLibrary.swift"

# Verify build environment
xcodebuild -version
swift --version
```

### Step 2: Begin Phase 1
```bash
# Task 1.1: Create NameCanonicalizer
# Task 1.2: Project cleanup
```

### Step 3: Track Progress
Update task status in tasks.json as you progress:
```json
"status": "in_progress"  // When starting
"status": "done"         // When complete
```

## ✅ Success Metrics

### Code Quality Targets
- **Test Coverage**: >= 90% for new components
- **TODO/FIXME Reduction**: > 50% reduction
- **Static Analysis**: Zero critical warnings

### Performance Targets
- **Project Size**: ~3MB reduction
- **Cache Hit Rate**: > 80% for recipe details
- **Generation Reliability**: > 95% success rate for per-meal counts

### Functionality Targets
- **Duplicate Prevention**: 100% within same category
- **Normalization**: 100% canonical naming consistency
- **Atomic Operations**: 100% success or full rollback

## ⚠️ Critical Dependencies

### Task Dependencies
- **1.1** (NameCanonicalizer) → **2.2, 3.1, 4.1**
- **2.1** (Transaction Wrapper) → **2.2**
- **4.1** (Cache Keys) → **4.2, 4.3**
- **6.1** (Unit Tests) → **6.2** → **6.3**

### File Path Verification
Before modifying, verify these paths exist:
- `Coordinator/AppCoordinator.swift`
- `Features/Profile/PreferencesEditView.swift`
- `Services/ServiceContainer.swift`

## 🔍 Testing Strategy

### Unit Testing (Task 6.1)
- NameCanonicalizer: Unicode, compounds, edge cases
- PantryService: Atomic operations, batch deduplication
- CacheKeyGenerator: Order independence verification
- RecipeDetailCache: TTL behavior, persistence

### Integration Testing (Task 6.2)
- End-to-end user flows
- Replace-on-add behavior
- Cache persistence across app restarts

### Validation Testing (Task 6.3)
- Clean build verification
- Performance regression checks
- Memory leak detection

## 📞 Support References

### Technical Specifications
- Complete requirements in `pantry_normalization_and_generation_plan.md`
- Implementation details in `tasks.json`

### Key Concepts
- **Name Canonicalization**: Unicode NFC, title-case, compound word handling
- **Replace-on-Add**: Same category + canonical name → replace (not insert)
- **Atomic Operations**: Transaction-wrapped with full rollback
- **Stable Caching**: Order-independent cache keys with SHA256

---

## 🎯 Implementation Success Checklist

- [ ] All 15 tasks completed in phase order
- [ ] Build succeeds on iOS 17+ target
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Performance metrics achieved
- [ ] Documentation updated
- [ ] Project size reduced by ~3MB
- [ ] Cache hit rate > 80%
- [ ] Generation reliability > 95%

**Estimated Timeline**: 14 days (2-3 weeks)
**Total Effort**: ~100 hours across 6 phases

Good luck with the implementation! 🚀