# V5 Implementation Checklist

## Phase 1: Foundation & Cleanup ✅ 
- [ ] **Task 1.1**: Implement NameCanonicalizer (8h)
  - [ ] Unicode NFC normalization with accented character preservation
  - [ ] Title-case with English stopwords (and, or, of, the, a, an, in)  
  - [ ] Acronym preservation (BBQ, A2 Milk)
  - [ ] Compound word handling with hyphen exceptions
  - [ ] >= 90% unit test coverage for edge cases
  - [ ] File created: `Utils/NameCanonicalizer.swift`

- [ ] **Task 1.2**: Project Cleanup - Build Artifacts (2h)
  - [ ] Remove build artifacts: .DS_Store, *.log, *.out, project.pbxproj.backup
  - [ ] Remove glob patterns: **/*.backup
  - [ ] Archive V4 Tasks/ directory to archive/v4-tasks/
  - [ ] Update .gitignore with new patterns
  - [ ] Validate ~3MB project size reduction
  - [ ] Run validation: `find . -name "*.log" -o -name "*.out" -o -name "*.backup" -print`

## Phase 2: Core Pantry Logic ✅
- [ ] **Task 2.1**: SwiftDataStorageService Transaction Wrapper (4h)
  - [ ] Add `performTransaction(_:)` wrapper for atomic operations
  - [ ] Implement rollback on failure functionality  
  - [ ] Ensure thread-safe operation
  - [ ] Error handling with proper Swift error types
  - [ ] File modified: `Services/SwiftDataStorageService.swift`

- [ ] **Task 2.2**: PantryService Replace-on-Add Logic (12h)
  - [ ] Same category + canonical name triggers replace (not insert)
  - [ ] Batch deduplication (first wins for same canonical key)
  - [ ] Atomic upsert with rollback on failure
  - [ ] Date reset (purchaseDate, dateAdded, notificationCycle = 0)
  - [ ] markAsRecentlyAdded works with updated items
  - [ ] **Simulation Tests**:
    - [ ] Mid-batch failure → No partial updates; pantry unchanged
    - [ ] Network failure during persist → Full rollback; no items modified
    - [ ] Duplicate canonical names in batch → First wins; subsequent ignored
  - [ ] File modified: `Services/PantryService.swift`
  - [ ] Dependencies: Tasks 1.1, 2.1

- [ ] **Task 2.3**: Wire Replace-on-Add to All Entry Points (8h)
  - [ ] Scan Results → addSelectedToPantry uses replace-on-add
  - [ ] Pantry Add sheet (library + custom) uses replace-on-add  
  - [ ] Manual add path uses replace-on-add
  - [ ] Recently added highlighting works correctly
  - [ ] Files modified: `Features/3_Results/ResultsViewModel.swift`, `Features/Pantry/PantryViewModel.swift`
  - [ ] Dependencies: Task 2.2

## Phase 3: Library & Legacy Cleanup ✅
- [ ] **Task 3.1**: IngredientLibrary Canonicalization (6h)
  - [ ] Load-time canonicalization for all library entries
  - [ ] categoryToItems uses canonical names, sorted case-insensitively
  - [ ] nameToCategory with canonical and lowercase keys
  - [ ] ingredients.txt removed from repo and Xcode project
  - [ ] Suggestions return canonicalized names
  - [ ] File modified: `Services/IngredientLibrary.swift`
  - [ ] File removed: `ingredients.txt`
  - [ ] Dependencies: Task 1.1

- [ ] **Task 3.2**: Remove Organizer Feature (4h)
  - [ ] PantryOrganizerService deleted
  - [ ] Organizer UI button removed from PantryView
  - [ ] Related models (UpdateOp, MergeOp, CleanUpPlan) deleted
  - [ ] ServiceContainer updated to remove organizer dependencies
  - [ ] Build succeeds without organizer references
  - [ ] Files modified: `Features/Pantry/PantryView.swift`, `Features/Pantry/PantryViewModel.swift`, `Services/ServiceContainer.swift`
  - [ ] Files removed: `Services/PantryOrganizerService.swift`, `Tests/PantryOrganizerServiceTests.swift`

## Phase 4: Generation & Caching ✅
- [ ] **Task 4.1**: Stable Cache Key Generator (4h)
  - [ ] Stable keys regardless of pantry item ordering
  - [ ] SHA256 hash of canonical sorted inputs
  - [ ] Separate hashes for pantry, preferences, meal specifications  
  - [ ] Unit tests verify order independence
  - [ ] Same pantry in different orders yields identical cache keys
  - [ ] File created: `Utils/CacheKeyGenerator.swift`

- [ ] **Task 4.2**: Per-Meal Generation with JSON Mode (16h)
  - [ ] generationConfig.response_mime_type = 'application/json' set
  - [ ] Per-meal counts propagated from UI to generation
  - [ ] Strict per-meal schema for selected meals only (unrequested meals absent)
  - [ ] JSON parsing with text fallback for compatibility
  - [ ] Single retry if meal array.count < N, then filler if still short
  - [ ] Files modified: `Services/RecipeGenerationService.swift`, `Services/RecipeRequestBuilder.swift`, `Services/RecipeServiceAdapter.swift`
  - [ ] Dependencies: Task 4.1

- [ ] **Task 4.3**: Disk Cache with TTL (10h)
  - [ ] Disk-backed cache with 48h TTL
  - [ ] Cache keyed by stable cache keys
  - [ ] Detail prefetch after ideas generation
  - [ ] Cache cleanup for expired entries
  - [ ] Thread-safe cache operations
  - [ ] File created: `Services/RecipeDetailCache.swift`
  - [ ] Dependencies: Task 4.1

## Phase 5: Documentation & Quality ✅
- [ ] **Task 5.1**: Documentation Audit (3h)
  - [ ] Outdated reports archived to archive/reports/
  - [ ] README.md reflects V5 architecture changes
  - [ ] Root directory cleaned of outdated documentation
  - [ ] No broken internal links (verified with link checker)
  - [ ] Files moved: auth_state_implementation_report.md, firebase_config_audit_report.md, iOS17_Upgrade_Optimization_Report.md, AI_Recipe_Generator_Progress_Tracker.md
  - [ ] Validation: `find . -name "*.md" -exec grep -l "\\[.*\\](.*\\.md)" {} \\; | head -5`

- [ ] **Task 5.2**: Technical Debt Cleanup (6h)
  - [ ] TODO/FIXME count reduced (conservative fixes only)
  - [ ] Static analysis warnings resolved (non-breaking changes)
  - [ ] Development-only debug code removed/consolidated
  - [ ] Build warnings eliminated
  - [ ] Files verified: `Coordinator/AppCoordinator.swift`, `Features/Profile/PreferencesEditView.swift`, `Services/ServiceContainer.swift`
  - [ ] Validation: `grep -r "TODO\\|FIXME" --include="*.swift" . | wc -l`

## Phase 6: Testing & Validation ✅
- [ ] **Task 6.1**: Unit Test Implementation (20h)
  - [ ] NameCanonicalizer: Unicode, stopwords, compounds, edge cases
  - [ ] PantryService: Atomic operations, batch deduplication, rollback
  - [ ] CacheKeyGenerator: Order independence, hash stability  
  - [ ] RecipeDetailCache: TTL behavior, disk persistence
  - [ ] Generation: Per-meal counts, JSON/text parsing, retry logic
  - [ ] Files created: `Tests/NameCanonicalizerTests.swift`, `Tests/CacheKeyGeneratorTests.swift`, `Tests/PantryServiceReplaceTests.swift`, `Tests/RecipeDetailCacheTests.swift`

- [ ] **Task 6.2**: Integration Testing (12h)
  - [ ] Scan Results → Pantry: Replace-on-add works, highlights correct items
  - [ ] Add Sheet → Pantry: Library/custom items normalized and replaced
  - [ ] Recipe Generation: Exact per-meal counts, caching effective
  - [ ] Cache persistence: Survives app restart within TTL
  - [ ] Dependencies: Task 6.1

- [ ] **Task 6.3**: Final Build & Regression Testing (8h)
  - [ ] Clean build: iOS 17+ target, Swift 5.9+, Xcode 15+
  - [ ] All unit tests pass
  - [ ] All integration tests pass  
  - [ ] Performance regression check
  - [ ] Memory leak verification
  - [ ] Validation: `xcodebuild -scheme IngredientScanner build`, `xcodebuild test -scheme IngredientScanner`
  - [ ] Dependencies: Task 6.2

## Final Validation Checklist ✅
- [ ] **Functionality**:
  - [ ] Adding identical item (same category, canonical name) replaces existing with date reset
  - [ ] Pantry names consistently canonicalized from all sources
  - [ ] IngredientLibrary suggestions show canonical names
  - [ ] Organizer button and functionality completely removed
  - [ ] Generator returns exactly N recipes per meal with JSON response mode
  - [ ] Detail view faster due to prefetch + cache
  
- [ ] **Quality Metrics**:
  - [ ] Build artifacts removed; project size reduced by ~3MB
  - [ ] Documentation consolidated; root directory cleaner  
  - [ ] TODO/FIXME count reduced by >50%; static analysis warnings resolved
  - [ ] Replace operations atomic (no partial updates on failure)
  - [ ] Cache keys stable regardless of pantry item ordering
  - [ ] JSON MIME responses parsed correctly; text fallback works
  - [ ] Batch operations deduplicate identical items within request

- [ ] **Performance Targets**:
  - [ ] Cache hit rate > 80% for recipe details
  - [ ] Generation reliability > 95% success rate for per-meal counts
  - [ ] Test coverage >= 90% for new components
  - [ ] 100% prevention of same-name duplicates within same category
  - [ ] 100% canonical naming consistency across all sources
  - [ ] 100% transaction success or full rollback

---

**Total Estimated Effort**: ~100 hours across 6 phases  
**Timeline**: 14 days (2-3 weeks)  
**Success Rate Target**: All checkboxes completed ✅