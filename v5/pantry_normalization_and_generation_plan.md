# V5 Plan — Pantry Name Normalization, Replace-on-Add, Library Cleanup, and Per‑Meal Generation Enforcement

Owner: Augment Agent
Date: 2025-09-04
Target: iOS 17+ only, Swift 5.9+, Xcode 15+
Scope: Pantry add flows, IngredientLibrary, Organizer removal, and Recipe generation (per‑meal counts + caching)

## 1) Objectives
- Eliminate name casing/format inconsistencies by normalizing on write.
- When re‑adding an identical item (same name/category), replace the existing one to restart days (no duplicates).
- Make IngredientLibrary suggestions consistent with storage by applying the same normalization and remove the legacy ingredients.txt file.
- Remove Organizer feature entirely (now redundant).
- Enforce “dishes per meal” so result counts match UI selections; add JSON response mode, retry, and detail prefetch/cache.
- Improve architecture for recipe detail generation: adopt a hybrid strategy (prefetch/cache) with an option to upgrade to unified complete generation when signals predict high likelihood of open.
- Introduce cost controls and caching strategy to minimize repeated AI calls across app restarts (disk cache keyed by pantry+prefs).

## 2) Canonicalization Rules (NameCanonicalizer)
Applied at write‑time and to library entries at load‑time.
- Whitespace: trim; collapse multiple spaces to a single space.
- Unicode & Locale:
  - Normalize input to NFC form; preserve accented characters (e.g., crème brûlée, jalapeño, naïve, café).
  - Title‑case using locale‑aware rules; stopwords list remains English‑focused for now.
- Characters:
  - Replace slashes ("/") and dashes ("-") with a single space.
  - Keep apostrophes (’ or ').
  - Keep numerals (0‑9).
  - Keep commas (,) as provided.
  - Leave parentheses and their contents as‑is (for now).
  - Remove any other non‑alphanumeric punctuation (e.g., “&”, "#", "!")
- Casing:
  - Title‑case words, with stopwords lowercased unless first word: [and, or, of, the, a, an, in].
  - Preserve acronyms/abbreviations if they appear fully uppercase (e.g., “BBQ Sauce”, "A2 Milk").
- Compound words:
  - Hyphens are removed by rule (e.g., “self‑rising” → “Self Rising”, “stir‑fry” → “Stir Fry”). We can add exceptions later if needed.
- Descriptor preservation: do not drop meaningful qualifiers (e.g., “Whole Milk”, “7 Grain Bread”).

Examples
- "whole milk" → "Whole Milk"
- "7‑grain bread" → "7 Grain Bread"
- "jam/jelly/preserves" → "Jam Jelly Preserves"
- "Confectioners’ sugar" → "Confectioners’ Sugar"
- "Canned tomatoes (diced, whole, crushed)" → kept with parentheses

## 3) Replace‑on‑Add (restart days)
- Matching: within the same category, match by normalizedName.lowercased(). Cross‑category matches always create a new item (no replace).
- Behavior on match:
  - Update existing item’s name to canonical form (in case rules changed it).
  - Reset purchaseDate and dateAdded to now; notificationCycle = 0.
  - Persist the update (single‑item replace in storage).
- Behavior on no match:
  - Insert as a new item (already canonicalized) and persist.
- Batch adds: when multiple inputs normalize to the same (name, category) in one operation, collapse to one replacement/insert deterministically (first wins) and ignore duplicates to avoid artificial multiplicity.
- Apply for all add flows: Scan Results, Pantry Add sheet (library + custom+Gemini), manual add.

## 4) IngredientLibrary Cleanup
- Source of truth: embedded Base64 data (557+ items across all categories). Remove ingredients.txt from the repo and project.
- Load‑time normalization: apply NameCanonicalizer to every parsed entry before populating:
  - categoryToItems[category] = [canonicalized names], sorted case‑insensitively
  - nameToCategory with keys for both canonical names and lowercased variants for lookup
- Suggestions and group(names:) return/use canonicalized names.
- Remove dev/local path fallbacks in loader; prefer embedded data; optional: keep bundle load but repo will not include ingredients.txt.

## 5) Organizer Removal
- Remove PantryOrganizerService, models (UpdateOp, MergeOp, CleanUpPlan), related tests.
- Remove Organizer UI (button) and state in PantryView / PantryViewModel.
- Delete any dangling imports/wiring.

## 6) Per‑Meal Generation Enforcement & Architecture (refined)
- Propagate per‑meal dish counts from UI into request building (Quick single bucket; Custom per‑meal counts with per‑meal max time, cuisines optional).
- Prompt contract changes (RecipeGenerationService):
  - Use a strict schema keyed by meal types (e.g., { "breakfast": [...], "lunch": [...], "dinner": [...] }).
  - “Generate exactly Ni recipes for each specified meal … Return only a compact JSON object with exactly the specified array lengths; no extra text.”
  - Set generationConfig.response_mime_type = "application/json" for Gemini requests.
- Output validation & retry:
  - If any meal array.count < Ni, retry once with explicit guidance (missing counts and already chosen titles).
  - If still short, fill remaining slots to Ni with simple pantry‑based variants to keep UI consistent.
- Detail generation strategy:
  - Hybrid model: after ideas generation, prefetch top K details and cache; keep on‑demand for the rest.
  - Optional future: unified generation mode that returns complete recipes when predictive signals (e.g., likelihood to open) exceed a threshold.
- Caching & cost controls:
  - Disk‑backed cache with TTL ~48h keyed by pantry hash + preferences hash (and per‑meal counts) + title for details; avoid repeat calls after relaunch.
  - Idea/menu caching keyed by the same pantry/prefs hash to reduce re‑gen when inputs unchanged.
  - Telemetry counters for idea/detail calls and retries to monitor API cost.
- Quality assurance pipeline (post‑generation validators):
  - Feasibility against pantry; diversity across meals; basic complexity guardrails using equipmentOwned; optional balance heuristics.
- Future cross‑meal optimization (V6):
  - MealPlanRequest { allowIngredientReuse, optimizeForMinimalShopping, considerLeftovers } with simple optimizer pass.

## 7) Implementation Steps
1) Implement NameCanonicalizer (Utils/NameCanonicalizer.swift) + unit tests.
2) Project cleanup (Immediate):
   - Verify files exist before cleanup: `ls -la build*.* *.log *.out **/*.backup` and `ls -la "V4 Tasks/"`
   - Remove build artifacts (.DS_Store, *.log, *.out, **/*.backup)
   - Archive V4 Tasks/ directory to archive/v4-tasks/ (confirm V4 tasks completed first)
   - Update .gitignore with additional patterns (*.log, *.out, .DS_Store, **/*.backup)
3) PantryService: add addOrReplaceIngredients(_:) using canonicalization and same‑category matching; update persistence; unit tests.
   - Add SwiftDataStorageService.performTransaction(_:) wrapper for atomic operations
   - Implement batch deduplication before processing (first wins for same canonical key)
   - Ensure atomic upsert with rollback on failure
4) Wire addOrReplace + canonicalization in:
   - Features/3_Results/ResultsViewModel.addSelectedToPantry()
   - Features/Pantry/PantryViewModel.saveAddSheet() (library + custom)
   - Manual add path (if present)
   - Ensure markAsRecentlyAdded uses the returned updated items
5) IngredientLibrary:
   - Apply canonicalization at parse/load; update suggest() and group(names:)
   - Remove ingredients.txt from repo and Xcode project; simplify loader fallbacks
   - Tests for canonicalized suggestions and mapping
6) Remove Organizer:
   - Delete service, models, UI/button, state; remove references and tests
   - Build and clean up project references
7) Generation (per‑meal):
   - RequestBuilder/Adapter: carry per‑meal counts into RecipeGenerationService.generateMealIdeas(requestedCounts:)
   - Update prompts to strict per‑meal schema; set generationConfig.response_mime_type = "application/json"
   - Implement stable cache key: sorted pantry + canonical preferences hash (Utils/CacheKeyGenerator.swift)
   - Add JSON parsing with text fallback for compatibility
   - Add validation, single retry, and filler; unit tests
   - Add detail prefetch and disk cache with 48h TTL; tests for cache TTL
8) Documentation audit:
   - Check for code references to reports being archived
   - Archive outdated reports to archive/reports/
   - Update README.md for V5 changes and current architecture
   - Clean up root-level markdown files
9) Technical debt cleanup:
   - Resolve TODO/FIXME in AppCoordinator.swift and PreferencesEditView.swift (conservative fixes only)
   - Remove development-only debug code; consolidate debug utilities
   - Run static analysis and fix non-breaking warnings; defer major model consolidation
10) Full build and run tests; fix regressions.
    - Validate: `xcodebuild -scheme IngredientScanner build`
    - Verify reduced TODO/FIXME count: `grep -r "TODO\|FIXME" --include="*.swift" . | wc -l`

## 8) UX Considerations (allowed if needed)
- No new buttons required for pantry flows. If users get confused by replace behavior (same row refreshes), optionally show a brief non‑intrusive “Refreshed” pill later. Not planned initially.
- Recipes detail fetch is prefetch/cached; no additional UI unless we later expose a “Load details” control.

## 9) Acceptance Criteria
- Adding an item that matches existing (same category, case‑insensitive normalized name) updates the existing row (date reset); no duplicate created.
- Pantry names appear consistently canonicalized regardless of source (scan, library, custom).
- IngredientLibrary suggestions show canonicalized names; search behaves identically or better.
- Organizer button and functionality are gone; build is green.
- Generator returns exactly Ni per meal (after one retry and, if needed, filler) and uses JSON response mode.
- Detail view feels faster due to prefetch + cache; subsequent opens within TTL do not call AI.
- Build artifacts removed; project size reduced by ~3MB.
- Documentation consolidated; root directory cleaner.
- TODO/FIXME count reduced; static analysis warnings resolved.
- Replace operations are atomic (no partial updates on failure).
- Cache keys are stable regardless of pantry item ordering.
- JSON MIME responses parsed correctly; text responses handled as fallback.
- Batch operations deduplicate identical items within single request.

## 10) Risks & Mitigations
- Token/latency pressure from larger prompts: use response_mime_type JSON mode, compact schemas, and pantry/prefs hashing to minimize re‑gen.
- Cultural adaptation: consider localized cuisine templates later; keep core prompts neutral.
- Documentation archival could break internal links: mitigated by keeping critical docs and updating references in README.md.
- Transaction failures leaving partial state: atomic operations with rollback prevent data corruption.
- Cache key instability from input ordering: canonical sorting before hashing ensures stable keys.
- JSON MIME compatibility issues: fallback to text parsing maintains backward compatibility.

## 11) Telemetry, Resilience & Privacy (focused for V5)
- Telemetry (debug): count add‑or‑replace events vs inserts; idea/detail calls; retries; filler usage (no PII). Add normalization before/after metrics and replacement-by-category.
- Resilience: add simple circuit‑breaker/backoff around Gemini calls; surface friendly fallback messages. Define error enums for normalization/library/cache failures and map to user-friendly copy.
- Privacy: non‑sensitive domain; avoid PII in requests; document data handling for completeness.

## 12) File Pointers (expected changes)
- Utils/NameCanonicalizer.swift (new)
- Utils/CacheKeyGenerator.swift (new - stable key generation)
- Services/PantryService.swift (addOrReplaceIngredients)
- Services/SwiftDataStorageService.swift (transaction wrapper)
- Features/3_Results/ResultsViewModel.swift (call addOrReplace)
- Features/Pantry/PantryViewModel.swift (call addOrReplace; canonicalize custom/library)
- Services/IngredientLibrary.swift (canonicalize on load; remove file fallback)
- Remove: Services/PantryOrganizerService.swift, related models, UI in Features/Pantry/PantryView*.swift, tests
- Services/RecipeGenerationService.swift, Services/RecipeRequestBuilder.swift, Services/RecipeServiceAdapter.swift (per‑meal counts + JSON mode)
- Add: Services/RecipeDetailCache.swift (TTL disk cache)
- archive/v4-tasks/ (new directory)
- archive/reports/ (new directory)
- .gitignore (updated patterns)
- README.md (updated for V5)

## 13) Testing Matrix
- Unit: NameCanonicalizer, PantryService upsert logic, IngredientLibrary normalization, generation per‑meal counts, detail cache TTL.
- Integration: Scan Results add flow replaces and highlights; Add sheet save replaces and highlights.
- Build: iOS 17+ target; Swift 5.9+; Xcode 15+.
- Quality: Pre/post TODO/FIXME count; static analysis warnings; project size reduction validation.
- Transaction: Atomic operations rollback on failure; batch deduplication correctness.
- Cache: Stable key generation regardless of input ordering; JSON/text parsing fallback.

